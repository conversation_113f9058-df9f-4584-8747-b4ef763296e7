# 文件传输SDK测试指南

本文档详细说明如何运行和验证文件传输SDK的所有功能，确保系统的正确性和性能。

## 测试概览

### 测试分类

1. **单元测试** - 测试各个组件的独立功能
2. **集成测试** - 测试组件间的协作
3. **端到端测试** - 测试完整的客户端-服务端交互
4. **性能测试** - 验证系统性能指标
5. **功能验证测试** - 验证README中描述的所有功能

### 测试覆盖范围

- ✅ 用户认证机制
- ✅ 文件上传下载
- ✅ 断点续传
- ✅ 分块传输
- ✅ 秒传功能
- ✅ 传输限速
- ✅ 进度监听
- ✅ 文件校验
- ✅ 管理接口
- ✅ 监控功能
- ✅ 异常处理
- ✅ 性能指标

## 快速开始

### 运行所有测试

```bash
# 运行完整测试套件
./run-tests.sh

# 仅运行单元测试
./run-tests.sh --unit-only

# 仅运行集成测试
./run-tests.sh --integration-only

# 包含性能测试
./run-tests.sh --performance

# 查看帮助
./run-tests.sh --help
```

### 手动运行测试

```bash
# 编译项目
mvn clean compile

# 运行服务端SDK测试
mvn test -pl file-transfer-server-sdk

# 运行客户端SDK测试
mvn test -pl file-transfer-client-sdk

# 运行Demo集成测试
mvn test -pl file-transfer-demo
```

## 详细测试说明

### 1. 管理接口测试

验证新增的管理接口功能：

```bash
# 启动Demo服务
cd file-transfer-demo
mvn spring-boot:run

# 在另一个终端测试管理接口
curl -H "X-File-Transfer-User: demo" \
     -H "X-File-Transfer-Auth: $(echo -n 'demo:**********:signature' | base64)" \
     http://localhost:49011/filetransfer/api/admin/statistics

curl -H "X-File-Transfer-User: demo" \
     -H "X-File-Transfer-Auth: $(echo -n 'demo:**********:signature' | base64)" \
     http://localhost:49011/filetransfer/api/admin/health

curl -H "X-File-Transfer-User: demo" \
     -H "X-File-Transfer-Auth: $(echo -n 'demo:**********:signature' | base64)" \
     http://localhost:49011/filetransfer/api/admin/clear-rate-limiters
```

### 2. 客户端SDK功能测试

```java
// 创建客户端配置
ClientConfig config = ClientConfigBuilder.localConfig("demo", "demo-secret-key-2024");

// 创建客户端
try (FileTransferClient client = new FileTransferClient(config)) {
    
    // 测试文件上传
    UploadResult uploadResult = client.uploadFileSync("./test-file.txt", null, null);
    assert uploadResult.isSuccess();
    
    // 测试文件下载
    DownloadResult downloadResult = client.downloadFileSync(
        uploadResult.getFileId(), "./downloaded-file.txt", null);
    assert downloadResult.isSuccess();
    
    // 测试进度查询
    TransferProgress progress = client.queryProgress(uploadResult.getTransferId());
    assert progress.isCompleted();
}
```

### 3. 性能测试

运行性能测试验证系统性能指标：

```bash
# 运行性能测试
mvn test -pl file-transfer-server-sdk -Dtest=FileTransferPerformanceTest

# 查看性能测试结果
cat file-transfer-server-sdk/target/surefire-reports/TEST-*.xml
```

### 4. 测试工具验证

验证README中描述的测试工具功能：

```java
import com.sdesrd.filetransfer.server.util.FileTransferTestUtils;

// 创建测试文件
File smallFile = FileTransferTestUtils.createSmallTestFile("./test/small.dat");
File mediumFile = FileTransferTestUtils.createMediumTestFile("./test/medium.dat");
File largeFile = FileTransferTestUtils.createLargeTestFile("./test/large.dat");

// 验证文件相等性
boolean isEqual = FileTransferTestUtils.verifyFilesEqual(
    "./original/file.dat", "./downloaded/file.dat");

// 清理测试环境
FileTransferTestUtils.cleanupTestDirectory("./test/");
```

## 测试结果验证

### 预期测试结果

1. **单元测试**: 所有测试应该通过，覆盖率 > 80%
2. **集成测试**: 端到端流程测试通过
3. **性能测试**: 
   - 文件上传/下载速度 > 1MB/s
   - MD5计算吞吐量 > 5MB/s
   - 内存使用合理（处理大文件时增长 < 100MB）
4. **功能测试**: 所有README描述的功能正常工作

### 测试报告

测试完成后，查看以下位置的测试报告：

```
file-transfer-server-sdk/target/surefire-reports/
file-transfer-client-sdk/target/surefire-reports/
file-transfer-demo/target/surefire-reports/
```

### 覆盖率报告

如果配置了JaCoCo，可以查看覆盖率报告：

```
file-transfer-server-sdk/target/site/jacoco/index.html
file-transfer-client-sdk/target/site/jacoco/index.html
```

## 故障排除

### 常见测试问题

1. **端口冲突**: 确保测试端口（49011, 49012）未被占用
2. **权限问题**: 确保有写入测试目录的权限
3. **内存不足**: 大文件测试可能需要更多内存，调整JVM参数
4. **网络问题**: 集成测试需要本地网络连接正常

### 调试技巧

```bash
# 启用详细日志
mvn test -Dlogging.level.com.sdesrd.filetransfer=DEBUG

# 运行单个测试类
mvn test -Dtest=FileTransferAdminControllerTest

# 运行单个测试方法
mvn test -Dtest=FileTransferAdminControllerTest#testGetStatistics_Success

# 跳过测试
mvn install -DskipTests
```

## 持续集成

### GitHub Actions配置示例

```yaml
name: 测试

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: 设置Java
      uses: actions/setup-java@v2
      with:
        java-version: '8'
        distribution: 'adopt'
    
    - name: 运行测试
      run: ./run-tests.sh
    
    - name: 上传测试报告
      uses: actions/upload-artifact@v2
      if: always()
      with:
        name: test-reports
        path: '**/target/surefire-reports/'
```

## 测试最佳实践

1. **测试隔离**: 每个测试使用独立的临时目录
2. **资源清理**: 测试后清理创建的文件和资源
3. **异常测试**: 验证错误情况的处理
4. **边界测试**: 测试极限情况（大文件、网络中断等）
5. **并发测试**: 验证多线程环境下的正确性

## 性能基准

### 硬件要求

- CPU: 2核心以上
- 内存: 4GB以上
- 磁盘: SSD推荐，至少1GB可用空间
- 网络: 100Mbps以上

### 性能指标

| 操作 | 最低要求 | 推荐性能 |
|------|----------|----------|
| 文件上传 | 1MB/s | 10MB/s |
| 文件下载 | 1MB/s | 10MB/s |
| MD5计算 | 5MB/s | 20MB/s |
| 文件复制 | 10MB/s | 50MB/s |
| 内存使用 | <100MB增长 | <50MB增长 |

## 总结

通过运行完整的测试套件，可以验证文件传输SDK的所有功能都按照README文档的描述正确实现。测试覆盖了从基础的文件操作到复杂的并发传输场景，确保系统的可靠性和性能。

如果所有测试都通过，说明：

1. ✅ README文档与实际实现一致
2. ✅ 所有核心功能正常工作
3. ✅ 系统性能满足要求
4. ✅ 错误处理机制完善
5. ✅ 代码质量达标

建议在每次代码变更后运行测试套件，确保系统的稳定性和可靠性。
