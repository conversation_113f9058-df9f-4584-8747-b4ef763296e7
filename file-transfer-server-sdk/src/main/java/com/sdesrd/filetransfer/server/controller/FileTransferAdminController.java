package com.sdesrd.filetransfer.server.controller;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sdesrd.filetransfer.server.dto.ApiResult;
import com.sdesrd.filetransfer.server.dto.SystemHealthResponse;
import com.sdesrd.filetransfer.server.interceptor.AuthInterceptor;
import com.sdesrd.filetransfer.server.service.FileTransferMonitorService;
import com.sdesrd.filetransfer.server.service.FileTransferMonitorService.TransferStatistics;
import com.sdesrd.filetransfer.server.service.FileTransferService;
import com.sdesrd.filetransfer.server.util.RateLimitUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件传输管理控制器
 * 提供系统监控和管理功能
 */
@Slf4j
@RestController
@RequestMapping("/filetransfer/api/admin")
// @Api(tags = "文件传输管理接口") - Swagger注解已移除
public class FileTransferAdminController {

    @Autowired
    private FileTransferMonitorService monitorService;
    
    @Autowired
    private FileTransferService fileTransferService;

    /**
     * 获取传输统计信息
     */
    @GetMapping("/statistics")
    // @ApiOperation("获取传输统计信息")
    public ApiResult<TransferStatistics> getStatistics(HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            log.debug("获取传输统计信息 - 用户: {}", currentUser);

            TransferStatistics statistics = monitorService.getTransferStatistics();
            return ApiResult.success(statistics);

        } catch (Exception e) {
            log.error("获取传输统计信息失败", e);
            return ApiResult.error("获取传输统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 系统健康检查（详细）
     */
    @GetMapping("/health")
    // @ApiOperation("系统健康检查（详细）")
    public ApiResult<SystemHealthResponse> systemHealth(HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            log.debug("系统健康检查 - 用户: {}", currentUser);

            // 构建详细的健康检查信息
            SystemHealthResponse healthResponse = new SystemHealthResponse();
            healthResponse.setStatus("UP");
            healthResponse.setTimestamp(System.currentTimeMillis());

            // 获取JVM内存信息
            Runtime runtime = Runtime.getRuntime();
            healthResponse.setTotalMemory(runtime.totalMemory());
            healthResponse.setFreeMemory(runtime.freeMemory());
            healthResponse.setUsedMemory(runtime.totalMemory() - runtime.freeMemory());
            healthResponse.setMaxMemory(runtime.maxMemory());

            // 获取传输统计信息
            TransferStatistics transferStats = monitorService.getTransferStatistics();
            healthResponse.setTransferStats(transferStats);

            return ApiResult.success(healthResponse);

        } catch (Exception e) {
            log.error("系统健康检查失败", e);
            return ApiResult.error("系统健康检查失败: " + e.getMessage());
        }
    }

    /**
     * 清理限流器缓存
     */
    @GetMapping("/clear-rate-limiters")
    // @ApiOperation("清理限流器缓存")
    public ApiResult<String> clearRateLimiters(HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            log.info("清理限流器缓存 - 用户: {}", currentUser);

            // 清理所有限流器
            RateLimitUtils.clearAllRateLimiters();

            log.info("限流器缓存清理完成 - 操作用户: {}", currentUser);
            return ApiResult.success("限流器缓存清理完成");

        } catch (Exception e) {
            log.error("清理限流器缓存失败", e);
            return ApiResult.error("清理限流器缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 重建SQLite数据库
     * 扫描存储目录下的文件，重建数据库记录
     * 注意：此操作会备份当前数据库
     */
    @PostMapping("/rebuild-database")
    // @ApiOperation("重建SQLite数据库")
    public ApiResult<Map<String, Object>> rebuildDatabase(HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            log.warn("开始重建数据库操作 - 操作用户: {}", currentUser);

            Map<String, Object> result = fileTransferService.rebuildDatabase();
            
            log.info("数据库重建操作完成 - 操作用户: {}, 结果: {}", currentUser, result);
            return ApiResult.success("数据库重建完成", result);

        } catch (Exception e) {
            log.error("重建数据库失败", e);
            return ApiResult.error("重建数据库失败: " + e.getMessage());
        }
    }
}