package com.sdesrd.filetransfer.server.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import com.sdesrd.filetransfer.server.service.AuthService;

import lombok.extern.slf4j.Slf4j;

/**
 * 认证拦截器
 */
@Slf4j
@Component
public class AuthInterceptor implements HandlerInterceptor {
    
    @Autowired
    private AuthService authService;
    
    private static final String AUTH_HEADER = "X-File-Transfer-Auth";
    private static final String USER_HEADER = "X-File-Transfer-User";
    private static final String CURRENT_USER_ATTR = "currentUser";
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 跳过健康检查和Swagger相关接口
        String requestPath = request.getRequestURI();
        if (isSkipAuth(requestPath)) {
            return true;
        }
        
        // 获取认证信息
        String username = request.getHeader(USER_HEADER);
        String authToken = request.getHeader(AUTH_HEADER);
        
        // 检查认证头是否存在
        if (username == null || username.trim().isEmpty() || authToken == null || authToken.trim().isEmpty()) {
            log.warn("缺少认证信息 - 路径: {}, 用户: {}, IP: {}", 
                    requestPath, username, getClientIp(request));
            
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"message\":\"缺少认证信息，请提供用户名和认证令牌\",\"data\":null}");
            return false;
        }
        
        // 验证认证信息
        if (!authService.authenticate(username, authToken)) {
            log.warn("认证失败 - 路径: {}, 用户: {}, IP: {}", 
                    requestPath, username, getClientIp(request));
            
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"message\":\"认证失败，用户名或密钥错误\",\"data\":null}");
            return false;
        }
        
        // 将当前用户信息存储到请求属性中
        request.setAttribute(CURRENT_USER_ATTR, username);
        
        log.debug("认证成功 - 路径: {}, 用户: {}", requestPath, username);
        return true;
    }
    
    /**
     * 判断是否跳过认证
     */
    private boolean isSkipAuth(String requestPath) {
        if (!StringUtils.hasText(requestPath)) {
            return false;
        }
        
        // 健康检查接口
        if (requestPath.contains("/actuator/") || requestPath.contains("/health") || requestPath.equals("/filetransfer/api/file/health")) {
            return true;
        }
        
        // 管理接口（注意：生产环境应该通过网络层面限制访问）
        if (requestPath.startsWith("/filetransfer/api/admin/")) {
            return true;
        }
        
        // Swagger文档接口
        if (requestPath.contains("/swagger-") || requestPath.contains("/v2/api-docs") 
                || requestPath.contains("/doc.html") || requestPath.contains("/webjars/")) {
            return true;
        }
        
        // 静态资源
        if (requestPath.contains("/static/") || requestPath.contains("/css/") 
                || requestPath.contains("/js/") || requestPath.contains("/images/")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
    
    /**
     * 从请求中获取当前用户
     */
    public static String getCurrentUser(HttpServletRequest request) {
        return (String) request.getAttribute(CURRENT_USER_ATTR);
    }
} 