package com.sdesrd.filetransfer.server.config;

import java.util.HashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;

import lombok.Data;

/**
 * 文件传输配置属性
 */
@Data
@ConfigurationProperties(prefix = "file.transfer.server")
public class FileTransferProperties {
    
    /**
     * 是否启用文件传输服务，默认启用
     */
    private boolean enabled = true;
    
    /**
     * 数据库文件路径，默认 ./data/file-transfer/database.db
     */
    private String databasePath = "./data/file-transfer/database.db";
    
    /**
     * 上传令牌有效期（毫秒），默认1小时
     */
    private long tokenExpire = 3600000L;
    
    /**
     * 清理过期传输记录的间隔（毫秒），默认1小时
     */
    private long cleanupInterval = 3600000L;
    
    /**
     * 传输记录过期时间（毫秒），默认24小时
     */
    private long recordExpireTime = 24 * 3600000L;
    
    /**
     * 服务器端口，默认49011
     */
    private int serverPort = 49011;
    
    /**
     * 服务器上下文路径，默认 /file-transfer
     */
    private String contextPath = "/file-transfer";
    
    /**
     * 是否启用Swagger文档，默认启用
     */
    private boolean swaggerEnabled = true;
    
    /**
     * 是否启用跨域支持，默认启用
     */
    private boolean corsEnabled = true;
    
    /**
     * 允许的跨域来源，默认允许所有
     */
    private String[] allowedOrigins = {"*"};
    
    /**
     * 默认配置（用于兜底）
     */
    private UserConfig defaultConfig = createDefaultConfig();
    
    /**
     * 多用户配置
     */
    private Map<String, UserConfig> users = new HashMap<>();
    
    /**
     * 创建默认配置
     */
    private UserConfig createDefaultConfig() {
        UserConfig config = new UserConfig();
        config.setStoragePath("./data/file-transfer/files");
        config.setUploadRateLimit(10485760L);  // 10MB/s
        config.setDownloadRateLimit(10485760L);
        config.setDefaultChunkSize(2097152L);  // 2MB
        config.setMaxFileSize(104857600L);     // 100MB
        config.setMaxInMemorySize(10485760L);  // 10MB
        config.setFastUploadEnabled(true);
        config.setRateLimitEnabled(true);
        return config;
    }
    
    /**
     * 获取用户配置，如果用户不存在则返回默认配置
     */
    public UserConfig getUserConfig(String username) {
        if (username == null || username.trim().isEmpty()) {
            return defaultConfig;
        }
        
        UserConfig userConfig = users.get(username);
        if (userConfig == null) {
            return defaultConfig;
        }
        
        // 用默认值填充空值
        return mergeWithDefault(userConfig);
    }
    
    /**
     * 将用户配置与默认配置合并
     */
    private UserConfig mergeWithDefault(UserConfig userConfig) {
        UserConfig merged = new UserConfig();
        merged.setSecretKey(userConfig.getSecretKey());
        merged.setStoragePath(userConfig.getStoragePath() != null ? userConfig.getStoragePath() : defaultConfig.getStoragePath());
        merged.setUploadRateLimit(userConfig.getUploadRateLimit() != null ? userConfig.getUploadRateLimit() : defaultConfig.getUploadRateLimit());
        merged.setDownloadRateLimit(userConfig.getDownloadRateLimit() != null ? userConfig.getDownloadRateLimit() : defaultConfig.getDownloadRateLimit());
        merged.setDefaultChunkSize(userConfig.getDefaultChunkSize() != null ? userConfig.getDefaultChunkSize() : defaultConfig.getDefaultChunkSize());
        merged.setMaxFileSize(userConfig.getMaxFileSize() != null ? userConfig.getMaxFileSize() : defaultConfig.getMaxFileSize());
        merged.setMaxInMemorySize(userConfig.getMaxInMemorySize() != null ? userConfig.getMaxInMemorySize() : defaultConfig.getMaxInMemorySize());
        merged.setFastUploadEnabled(userConfig.getFastUploadEnabled() != null ? userConfig.getFastUploadEnabled() : defaultConfig.getFastUploadEnabled());
        merged.setRateLimitEnabled(userConfig.getRateLimitEnabled() != null ? userConfig.getRateLimitEnabled() : defaultConfig.getRateLimitEnabled());
        return merged;
    }
    
    /**
     * 验证用户密钥
     */
    public boolean validateUser(String username, String secretKey) {
        if (username == null || secretKey == null) {
            return false;
        }
        
        UserConfig userConfig = users.get(username);
        if (userConfig == null) {
            return false;
        }
        
        return secretKey.equals(userConfig.getSecretKey());
    }
} 