package com.sdesrd.filetransfer.server.config;

import lombok.Data;

/**
 * 单个用户配置
 */
@Data
public class UserConfig {
    
    /**
     * 用户密钥
     */
    private String secretKey;
    
    /**
     * 文件存储路径
     */
    private String storagePath;
    
    /**
     * 上传速度限制（字节/秒）
     */
    private Long uploadRateLimit;
    
    /**
     * 下载速度限制（字节/秒）
     */
    private Long downloadRateLimit;
    
    /**
     * 默认分块大小（字节）
     */
    private Long defaultChunkSize;
    
    /**
     * 最大文件大小（字节）
     */
    private Long maxFileSize;
    
    /**
     * 最大内存大小（字节）
     */
    private Long maxInMemorySize;
    
    /**
     * 是否启用秒传功能
     */
    private Boolean fastUploadEnabled;
    
    /**
     * 是否启用限流
     */
    private Boolean rateLimitEnabled;
} 