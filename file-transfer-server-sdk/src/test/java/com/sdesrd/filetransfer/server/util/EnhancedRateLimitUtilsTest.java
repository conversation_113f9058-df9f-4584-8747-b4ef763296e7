package com.sdesrd.filetransfer.server.util;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import lombok.extern.slf4j.Slf4j;

/**
 * 增强的限流工具测试
 */
@Slf4j
@DisplayName("增强的限流工具测试")
class EnhancedRateLimitUtilsTest {
    
    /** 测试用户名常量 */
    private static final String TEST_USER_1 = "testUser1";
    private static final String TEST_USER_2 = "testUser2";
    
    /** 测试限速常量 */
    private static final long RATE_LIMIT_1MB = 1024 * 1024L; // 1MB/s
    private static final long RATE_LIMIT_5MB = 5 * 1024 * 1024L; // 5MB/s
    
    /** 测试数据大小常量 */
    private static final long DATA_SIZE_1KB = 1024L;
    private static final long DATA_SIZE_1MB = 1024 * 1024L;
    
    @BeforeEach
    void setUp() {
        log.info("[测试准备] 清理所有限流器");
        RateLimitUtils.clearAllRateLimiters();
    }
    
    @AfterEach
    void tearDown() {
        log.info("[测试清理] 清理所有限流器");
        RateLimitUtils.clearAllRateLimiters();
    }
    
    @Test
    @DisplayName("测试基本限流功能")
    void testBasicRateLimit() {
        log.info("[测试开始] 测试基本限流功能");
        
        String uploadKey = TEST_USER_1 + "_upload";
        
        // 测试限流应用
        long startTime = System.currentTimeMillis();
        log.info("[测试步骤] 应用限流 - 用户: {}, 限速: {}/s, 数据: {}", 
                TEST_USER_1, FileUtils.formatFileSize(RATE_LIMIT_1MB), FileUtils.formatFileSize(DATA_SIZE_1KB));
        
        RateLimitUtils.applyRateLimit(uploadKey, RATE_LIMIT_1MB, DATA_SIZE_1KB);
        
        long duration = System.currentTimeMillis() - startTime;
        log.info("限流耗时: {}ms", duration);
        
        // 验证限流器已创建
        assertEquals(1, RateLimitUtils.getRateLimiterCount(), "应该创建1个限流器");
        
        // 验证限流器速率
        double currentRate = RateLimitUtils.getCurrentRate(uploadKey);
        assertEquals(RATE_LIMIT_1MB, currentRate, 0.1, "限流器速率应该正确设置");
        
        log.info("[测试完成] 基本限流功能测试通过");
    }
    
    @Test
    @DisplayName("测试带超时的限流功能")
    void testRateLimitWithTimeout() {
        log.info("[测试开始] 测试带超时的限流功能");
        
        String downloadKey = TEST_USER_1 + "_download";
        long timeoutMs = 100L; // 100ms超时
        
        // 测试正常情况（应该成功）
        log.info("[测试步骤] 测试正常限流（应该成功）");
        boolean success1 = RateLimitUtils.applyRateLimitWithTimeout(
                downloadKey, RATE_LIMIT_5MB, DATA_SIZE_1KB, timeoutMs);
        
        assertTrue(success1, "正常限流应该成功");
        
        // 测试超时情况（请求大量数据）
        log.info("[测试步骤] 测试超时限流（应该超时）");
        boolean success2 = RateLimitUtils.applyRateLimitWithTimeout(
                downloadKey, RATE_LIMIT_1MB, DATA_SIZE_1MB, timeoutMs);
        
        assertFalse(success2, "大量数据请求应该超时");
        
        log.info("[测试完成] 带超时的限流功能测试通过");
    }
    
    @Test
    @DisplayName("测试多用户限流隔离")
    void testMultiUserRateLimit() {
        log.info("[测试开始] 测试多用户限流隔离");
        
        String user1UploadKey = TEST_USER_1 + "_upload";
        String user2UploadKey = TEST_USER_2 + "_upload";
        
        // 为不同用户设置不同限速
        log.info("[测试步骤] 为用户1设置限速: {}/s", FileUtils.formatFileSize(RATE_LIMIT_1MB));
        RateLimitUtils.applyRateLimit(user1UploadKey, RATE_LIMIT_1MB, DATA_SIZE_1KB);
        
        log.info("[测试步骤] 为用户2设置限速: {}/s", FileUtils.formatFileSize(RATE_LIMIT_5MB));
        RateLimitUtils.applyRateLimit(user2UploadKey, RATE_LIMIT_5MB, DATA_SIZE_1KB);
        
        // 验证限流器数量
        assertEquals(2, RateLimitUtils.getRateLimiterCount(), "应该创建2个限流器");
        
        // 验证不同用户的限速设置
        double user1Rate = RateLimitUtils.getCurrentRate(user1UploadKey);
        double user2Rate = RateLimitUtils.getCurrentRate(user2UploadKey);
        
        assertEquals(RATE_LIMIT_1MB, user1Rate, 0.1, "用户1限速应该正确");
        assertEquals(RATE_LIMIT_5MB, user2Rate, 0.1, "用户2限速应该正确");
        
        log.info("[测试完成] 多用户限流隔离测试通过");
        log.info("  - 用户1限速: {}/s", FileUtils.formatFileSize((long) user1Rate));
        log.info("  - 用户2限速: {}/s", FileUtils.formatFileSize((long) user2Rate));
    }
    
    @Test
    @DisplayName("测试用户限流器清理")
    void testUserRateLimiterCleanup() {
        log.info("[测试开始] 测试用户限流器清理");
        
        // 为用户创建上传和下载限流器
        String user1UploadKey = TEST_USER_1 + "_upload";
        String user1DownloadKey = TEST_USER_1 + "_download";
        String user2UploadKey = TEST_USER_2 + "_upload";
        
        log.info("[测试步骤] 创建多个限流器");
        RateLimitUtils.applyRateLimit(user1UploadKey, RATE_LIMIT_1MB, DATA_SIZE_1KB);
        RateLimitUtils.applyRateLimit(user1DownloadKey, RATE_LIMIT_1MB, DATA_SIZE_1KB);
        RateLimitUtils.applyRateLimit(user2UploadKey, RATE_LIMIT_5MB, DATA_SIZE_1KB);
        
        assertEquals(3, RateLimitUtils.getRateLimiterCount(), "应该创建3个限流器");
        
        // 清理用户1的限流器
        log.info("[测试步骤] 清理用户1的限流器");
        RateLimitUtils.clearUserRateLimiters(TEST_USER_1);
        
        assertEquals(1, RateLimitUtils.getRateLimiterCount(), "应该剩余1个限流器");
        
        // 验证用户1的限流器已清理
        assertEquals(-1, RateLimitUtils.getCurrentRate(user1UploadKey), "用户1上传限流器应该已清理");
        assertEquals(-1, RateLimitUtils.getCurrentRate(user1DownloadKey), "用户1下载限流器应该已清理");
        
        // 验证用户2的限流器仍存在
        assertEquals(RATE_LIMIT_5MB, RateLimitUtils.getCurrentRate(user2UploadKey), 0.1, 
                "用户2限流器应该仍然存在");
        
        log.info("[测试完成] 用户限流器清理测试通过");
    }
    
    @Test
    @DisplayName("测试限流器统计信息")
    void testRateLimiterStatistics() {
        log.info("[测试开始] 测试限流器统计信息");
        
        // 创建多个限流器
        log.info("[测试步骤] 创建多个限流器");
        RateLimitUtils.applyRateLimit(TEST_USER_1 + "_upload", RATE_LIMIT_1MB, DATA_SIZE_1KB);
        RateLimitUtils.applyRateLimit(TEST_USER_1 + "_download", RATE_LIMIT_1MB, DATA_SIZE_1KB);
        RateLimitUtils.applyRateLimit(TEST_USER_2 + "_upload", RATE_LIMIT_5MB, DATA_SIZE_1KB);
        
        // 获取统计信息
        String stats = RateLimitUtils.getRateLimiterStats();
        log.info("限流器统计: {}", stats);
        
        assertNotNull(stats, "统计信息不应该为空");
        assertTrue(stats.contains("限流器统计"), "统计信息应该包含标题");
        assertTrue(stats.contains("总数: 3"), "统计信息应该显示正确的总数");
        
        // 验证限流器数量
        assertEquals(3, RateLimitUtils.getRateLimiterCount(), "限流器数量应该正确");
        
        log.info("[测试完成] 限流器统计信息测试通过");
    }
    
    @Test
    @DisplayName("测试无限速情况")
    void testNoRateLimit() {
        log.info("[测试开始] 测试无限速情况");
        
        String key = TEST_USER_1 + "_upload";
        
        // 测试限速为0的情况
        log.info("[测试步骤] 测试限速为0（不限速）");
        long startTime = System.currentTimeMillis();
        RateLimitUtils.applyRateLimit(key, 0, DATA_SIZE_1MB);
        long duration = System.currentTimeMillis() - startTime;
        
        assertTrue(duration < 100, "不限速时应该立即返回，实际耗时: " + duration + "ms");
        assertEquals(0, RateLimitUtils.getRateLimiterCount(), "不限速时不应该创建限流器");
        
        // 测试负数限速的情况
        log.info("[测试步骤] 测试负数限速（不限速）");
        startTime = System.currentTimeMillis();
        RateLimitUtils.applyRateLimit(key, -1, DATA_SIZE_1MB);
        duration = System.currentTimeMillis() - startTime;
        
        assertTrue(duration < 100, "负数限速时应该立即返回，实际耗时: " + duration + "ms");
        assertEquals(0, RateLimitUtils.getRateLimiterCount(), "负数限速时不应该创建限流器");
        
        log.info("[测试完成] 无限速情况测试通过");
    }
    
    @Test
    @DisplayName("测试限流器动态速率更新")
    void testDynamicRateUpdate() {
        log.info("[测试开始] 测试限流器动态速率更新");
        
        String key = TEST_USER_1 + "_upload";
        
        // 初始设置
        log.info("[测试步骤] 初始设置限速: {}/s", FileUtils.formatFileSize(RATE_LIMIT_1MB));
        RateLimitUtils.applyRateLimit(key, RATE_LIMIT_1MB, DATA_SIZE_1KB);
        
        assertEquals(RATE_LIMIT_1MB, RateLimitUtils.getCurrentRate(key), 0.1, "初始限速应该正确");
        
        // 动态更新速率
        log.info("[测试步骤] 更新限速: {}/s", FileUtils.formatFileSize(RATE_LIMIT_5MB));
        RateLimitUtils.applyRateLimit(key, RATE_LIMIT_5MB, DATA_SIZE_1KB);
        
        assertEquals(RATE_LIMIT_5MB, RateLimitUtils.getCurrentRate(key), 0.1, "更新后限速应该正确");
        assertEquals(1, RateLimitUtils.getRateLimiterCount(), "应该仍然只有1个限流器");
        
        log.info("[测试完成] 限流器动态速率更新测试通过");
    }
    
    @Test
    @DisplayName("测试限流性能")
    void testRateLimitPerformance() {
        log.info("[测试开始] 测试限流性能");
        
        String key = TEST_USER_1 + "_upload";
        int requestCount = 1000;
        long requestSize = 1024L; // 1KB per request
        
        log.info("[测试步骤] 执行{}次限流请求，每次{}字节", requestCount, requestSize);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < requestCount; i++) {
            RateLimitUtils.applyRateLimit(key, RATE_LIMIT_5MB, requestSize);
        }
        
        long duration = System.currentTimeMillis() - startTime;
        double avgTime = (double) duration / requestCount;
        
        log.info("性能测试结果:");
        log.info("  - 总耗时: {}ms", duration);
        log.info("  - 平均每次: {:.2f}ms", avgTime);
        log.info("  - 吞吐量: {:.0f} 请求/秒", 1000.0 / avgTime);
        
        // 性能验证（平均每次请求应该在合理范围内）
        assertTrue(avgTime < 10, "平均每次限流请求应该小于10ms，实际: " + avgTime + "ms");
        
        log.info("[测试完成] 限流性能测试通过");
    }
}
