#!/bin/bash

# ================================================================================
# 文件传输SDK统一构建和测试脚本
# ================================================================================

set -e  # 遇到错误立即退出

# ==================== 常量定义 ====================

# 脚本版本信息
readonly SCRIPT_VERSION="2.0.0"
readonly SCRIPT_NAME="文件传输SDK统一构建和测试脚本"

# 默认Java 8 JDK路径
readonly DEFAULT_JAVA8_HOME="$HOME/.jdks/corretto-1.8.0_452"

# 项目模块列表
readonly PROJECT_MODULES=(
    "file-transfer-server-sdk"
    "file-transfer-client-sdk"
    "file-transfer-demo"
)

# 独立模块列表
readonly STANDALONE_MODULES=(
    "file-transfer-server-standalone"
)

# 超时配置
readonly BUILD_TIMEOUT=600    # 构建超时时间（秒）
readonly TEST_TIMEOUT=1200    # 测试超时时间（秒）
readonly SERVER_STARTUP_TIMEOUT=30  # 服务器启动超时时间（秒）

# 端口配置
readonly TEST_SERVER_PORT=49012  # 测试服务器端口

# 目录配置
readonly LOG_DIR="./logs"
readonly MAIN_LOG="$LOG_DIR/build-and-test-$(date +%Y%m%d_%H%M%S).log"
readonly COVERAGE_REPORT_DIR="$LOG_DIR/coverage"

# ==================== 颜色定义 ====================

readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[1;33m'
readonly COLOR_BLUE='\033[0;34m'
readonly COLOR_PURPLE='\033[0;35m'
readonly COLOR_CYAN='\033[0;36m'
readonly COLOR_NC='\033[0m' # No Color

# ==================== 日志函数 ====================

# 信息日志
log_info() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_BLUE}[INFO]${COLOR_NC} ${timestamp} - ${message}"
    echo "[INFO] ${timestamp} - ${message}" >> "$MAIN_LOG"
}

# 成功日志
log_success() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_GREEN}[SUCCESS]${COLOR_NC} ${timestamp} - ${message}"
    echo "[SUCCESS] ${timestamp} - ${message}" >> "$MAIN_LOG"
}

# 警告日志
log_warning() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_YELLOW}[WARNING]${COLOR_NC} ${timestamp} - ${message}"
    echo "[WARNING] ${timestamp} - ${message}" >> "$MAIN_LOG"
}

# 错误日志
log_error() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_RED}[ERROR]${COLOR_NC} ${timestamp} - ${message}"
    echo "[ERROR] ${timestamp} - ${message}" >> "$MAIN_LOG"
}

# 步骤日志
log_step() {
    local step_number="$1"
    local step_name="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_PURPLE}[STEP ${step_number}]${COLOR_NC} ${timestamp} - ${step_name}"
    echo "[STEP ${step_number}] ${timestamp} - ${step_name}" >> "$MAIN_LOG"
}

# ==================== 工具函数 ====================

# 初始化日志目录
init_logging() {
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR"
    fi
    
    if [ ! -d "$COVERAGE_REPORT_DIR" ]; then
        mkdir -p "$COVERAGE_REPORT_DIR"
    fi
    
    # 创建主日志文件
    touch "$MAIN_LOG"
    log_info "主日志文件：$MAIN_LOG"
}

# 显示脚本头部信息
show_header() {
    echo "========================================================"
    echo "    $SCRIPT_NAME"
    echo "    版本：$SCRIPT_VERSION"
    echo "    时间：$(date '+%Y-%m-%d %H:%M:%S')"
    echo "========================================================"
}

# 检查命令是否存在
check_command() {
    local command="$1"
    local description="$2"
    
    if ! command -v "$command" &> /dev/null; then
        log_error "$description 未安装或未在PATH中：$command"
        return 1
    fi
    return 0
}

# ==================== 环境检查函数 ====================

# 设置Java环境
setup_java_environment() {
    local custom_java_home="$1"
    
    log_step "1" "设置Java环境"
    
    # 如果指定了自定义Java路径，使用它
    if [ -n "$custom_java_home" ]; then
        if [ -d "$custom_java_home" ] && [ -x "$custom_java_home/bin/java" ]; then
            export JAVA_HOME="$custom_java_home"
            export PATH="$custom_java_home/bin:$PATH"
            log_info "使用指定的Java JDK：$custom_java_home"
        else
            log_error "指定的Java JDK路径无效：$custom_java_home"
            return 1
        fi
    # 检查默认的Java 8 JDK是否存在
    elif [ -d "$DEFAULT_JAVA8_HOME" ] && [ -x "$DEFAULT_JAVA8_HOME/bin/java" ]; then
        export JAVA_HOME="$DEFAULT_JAVA8_HOME"
        export PATH="$DEFAULT_JAVA8_HOME/bin:$PATH"
        log_info "使用默认的Java 8 JDK：$DEFAULT_JAVA8_HOME"
    # 使用系统默认Java
    else
        log_warning "未找到默认Java 8 JDK，使用系统默认Java"
        log_info "确保系统Java版本兼容项目要求"
    fi
    
    # 验证Java命令可用性
    if ! check_command "java" "Java运行时"; then
        return 1
    fi
    
    # 获取Java版本信息
    local java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    log_info "当前Java版本：$java_version"
    
    # 验证Java版本兼容性
    if [[ "$java_version" =~ ^1\.8\. ]]; then
        log_success "使用Java 8，完全兼容"
    elif [[ "$java_version" =~ ^(11|17|21)\. ]]; then
        log_warning "使用Java $java_version，项目配置为Java 8，但应该向后兼容"
    else
        log_warning "当前Java版本：$java_version，可能存在兼容性问题"
    fi
    
    return 0
}

# 检查Maven环境
check_maven_environment() {
    log_step "2" "检查Maven环境"
    
    # 检查Maven命令
    if ! check_command "mvn" "Apache Maven"; then
        return 1
    fi
    
    # 获取Maven版本信息
    local maven_version=$(mvn -version | head -n 1)
    log_info "Maven版本：$maven_version"
    
    # 配置Maven使用Java 8
    export MAVEN_OPTS="-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxPermSize=512m"
    
    if [ -n "$JAVA_HOME" ]; then
        export MAVEN_OPTS="$MAVEN_OPTS -Djava.home=$JAVA_HOME"
        log_info "Maven配置使用Java：$JAVA_HOME"
    fi
    
    log_info "Maven选项：$MAVEN_OPTS"
    log_success "Maven环境检查完成"
    
    return 0
}

# 验证项目结构
validate_project_structure() {
    log_step "3" "验证项目结构"
    
    # 检查根目录pom.xml
    if [ ! -f "pom.xml" ]; then
        log_error "根目录pom.xml文件不存在"
        return 1
    fi
    log_info "根目录pom.xml文件存在"
    
    # 检查各个模块目录
    local missing_modules=()
    for module in "${PROJECT_MODULES[@]}"; do
        if [ ! -d "$module" ]; then
            missing_modules+=("$module")
        else
            log_info "模块目录存在：$module"
            
            # 检查模块的pom.xml
            if [ ! -f "$module/pom.xml" ]; then
                log_warning "模块pom.xml不存在：$module/pom.xml"
            fi
        fi
    done
    
    # 报告缺失的模块
    if [ ${#missing_modules[@]} -gt 0 ]; then
        log_warning "以下模块目录不存在：${missing_modules[*]}"
        log_warning "将跳过这些模块的编译"
    fi
    
    log_success "项目结构验证完成"
    return 0
}

# 清理构建和测试环境
clean_environment() {
    log_step "4" "清理构建和测试环境"
    
    # 清理Maven构建缓存
    log_info "清理Maven构建缓存..."
    
    # 清理根目录target
    if [ -d "target" ]; then
        rm -rf target
        log_info "清理根目录target目录"
    fi
    
    # 清理各模块的target目录
    for module in "${PROJECT_MODULES[@]}"; do
        if [ -d "$module" ] && [ -d "$module/target" ]; then
            rm -rf "$module/target"
            log_info "清理模块target目录：$module"
        fi
    done
    
    # 清理测试数据目录
    local test_data_dirs=("./test-data" "./data" "./file-transfer-demo/data")
    for dir in "${test_data_dirs[@]}"; do
        if [ -d "$dir" ]; then
            rm -rf "$dir"
            log_info "清理测试数据目录：$dir"
        fi
    done
    
    # 清理临时文件
    find . -name "*.tmp" -delete 2>/dev/null || true
    find . -name "test-*.dat" -delete 2>/dev/null || true
    find . -name "*.test" -delete 2>/dev/null || true
    find . -name "*.log" -path "*/target/*" -delete 2>/dev/null || true
    
    # 停止可能运行的测试服务器
    local test_server_pids=$(lsof -ti:$TEST_SERVER_PORT 2>/dev/null || true)
    if [ -n "$test_server_pids" ]; then
        log_info "停止占用端口 $TEST_SERVER_PORT 的进程：$test_server_pids"
        kill -9 $test_server_pids 2>/dev/null || true
        sleep 2
    fi
    
    log_success "环境清理完成"
    return 0
}

# ==================== 构建功能函数 ====================

# 编译项目
compile_project() {
    log_step "5" "编译项目"

    local start_time=$(date +%s)

    log_info "开始编译整个项目..."
    log_info "编译命令：mvn clean compile -T 1C"

    # 执行Maven编译，使用并行编译提高速度
    if timeout "$BUILD_TIMEOUT" mvn clean compile -T 1C \
        -Dmaven.compiler.source=1.8 \
        -Dmaven.compiler.target=1.8 \
        -Dmaven.compiler.encoding=UTF-8 \
        -Dproject.build.sourceEncoding=UTF-8 \
        >> "$MAIN_LOG" 2>&1; then

        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        log_success "项目编译成功，耗时：${duration}秒"
        return 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        log_error "项目编译失败，耗时：${duration}秒"
        log_error "详细错误信息请查看日志文件：$MAIN_LOG"
        return 1
    fi
}

# 安装项目到本地仓库
install_project() {
    log_step "6" "安装项目到本地Maven仓库"

    local start_time=$(date +%s)

    log_info "开始安装项目到本地Maven仓库..."
    log_info "安装命令：mvn install -DskipTests -T 1C"

    # 执行Maven安装，跳过测试以提高速度
    if timeout "$BUILD_TIMEOUT" mvn install -DskipTests -T 1C \
        -Dmaven.compiler.source=1.8 \
        -Dmaven.compiler.target=1.8 \
        -Dmaven.compiler.encoding=UTF-8 \
        -Dproject.build.sourceEncoding=UTF-8 \
        >> "$MAIN_LOG" 2>&1; then

        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        log_success "项目安装成功，耗时：${duration}秒"
        return 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        log_error "项目安装失败，耗时：${duration}秒"
        log_error "详细错误信息请查看日志文件：$MAIN_LOG"
        return 1
    fi
}

# 验证构建结果
verify_build_results() {
    log_step "7" "验证构建结果"

    local success_count=0
    local total_count=0

    # 检查各模块的编译结果
    for module in "${PROJECT_MODULES[@]}"; do
        if [ ! -d "$module" ]; then
            continue
        fi

        total_count=$((total_count + 1))

        # 检查target/classes目录是否存在
        if [ -d "$module/target/classes" ]; then
            local class_count=$(find "$module/target/classes" -name "*.class" | wc -l)
            if [ "$class_count" -gt 0 ]; then
                log_info "模块编译成功：$module (生成 $class_count 个class文件)"
                success_count=$((success_count + 1))
            else
                log_warning "模块编译异常：$module (未生成class文件)"
            fi
        else
            log_warning "模块编译失败：$module (target/classes目录不存在)"
        fi

        # 检查JAR文件是否生成
        if [ -d "$module/target" ]; then
            local jar_files=$(find "$module/target" -name "*.jar" | wc -l)
            if [ "$jar_files" -gt 0 ]; then
                log_info "模块JAR文件生成：$module ($jar_files 个JAR文件)"
            fi
        fi
    done

    # 输出验证结果
    log_info "编译验证结果：$success_count/$total_count 个模块编译成功"

    if [ "$success_count" -eq "$total_count" ]; then
        log_success "所有模块编译验证通过"
        return 0
    else
        log_warning "部分模块编译验证失败"
        return 1
    fi
}

# ==================== 测试功能函数 ====================

# 运行单元测试
run_unit_tests() {
    log_step "8" "运行单元测试"

    local total_modules=0
    local success_modules=0
    local start_time=$(date +%s)

    for module in "${PROJECT_MODULES[@]}"; do
        if [ ! -d "$module" ]; then
            log_warning "模块目录不存在，跳过：$module"
            continue
        fi

        total_modules=$((total_modules + 1))

        log_info "运行模块单元测试：$module"

        # 运行单元测试，排除集成测试
        if timeout "$TEST_TIMEOUT" mvn test -pl "$module" \
            -Dmaven.compiler.source=1.8 \
            -Dmaven.compiler.target=1.8 \
            -Dtest='!**/*IntegrationTest,!**/*EndToEndTest' \
            >> "$MAIN_LOG" 2>&1; then

            log_success "模块单元测试通过：$module"
            success_modules=$((success_modules + 1))
        else
            log_error "模块单元测试失败：$module"
        fi
    done

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    log_info "单元测试结果：$success_modules/$total_modules 个模块通过，耗时：${duration}秒"

    if [ "$success_modules" -eq "$total_modules" ]; then
        log_success "所有单元测试通过"
        return 0
    else
        log_error "部分单元测试失败"
        return 1
    fi
}

# 启动测试服务器
start_test_server() {
    log_info "启动测试服务器（端口：$TEST_SERVER_PORT）..."

    # 确保端口未被占用
    if lsof -ti:$TEST_SERVER_PORT >/dev/null 2>&1; then
        log_error "端口 $TEST_SERVER_PORT 已被占用"
        return 1
    fi

    # 启动Demo服务器作为测试服务器
    mvn spring-boot:run -pl file-transfer-demo \
        -Dspring-boot.run.arguments="--server.port=$TEST_SERVER_PORT --logging.level.com.sdesrd.filetransfer=INFO" \
        >> "$MAIN_LOG" 2>&1 &

    local server_pid=$!
    echo $server_pid > "$LOG_DIR/test-server.pid"

    # 等待服务器启动
    log_info "等待测试服务器启动（PID：$server_pid）..."
    local wait_count=0
    while [ $wait_count -lt $SERVER_STARTUP_TIMEOUT ]; do
        if curl -s "http://localhost:$TEST_SERVER_PORT/filetransfer/api/file/health" >/dev/null 2>&1; then
            log_success "测试服务器启动成功"
            return 0
        fi

        sleep 1
        wait_count=$((wait_count + 1))

        # 检查进程是否还在运行
        if ! kill -0 $server_pid 2>/dev/null; then
            log_error "测试服务器进程意外退出"
            return 1
        fi
    done

    log_error "测试服务器启动超时"
    kill $server_pid 2>/dev/null || true
    return 1
}

# 停止测试服务器
stop_test_server() {
    log_info "停止测试服务器..."

    if [ -f "$LOG_DIR/test-server.pid" ]; then
        local server_pid=$(cat "$LOG_DIR/test-server.pid")
        if kill -0 $server_pid 2>/dev/null; then
            kill $server_pid 2>/dev/null || true
            sleep 3

            # 强制杀死如果还在运行
            if kill -0 $server_pid 2>/dev/null; then
                kill -9 $server_pid 2>/dev/null || true
            fi

            log_info "测试服务器已停止（PID：$server_pid）"
        fi
        rm -f "$LOG_DIR/test-server.pid"
    fi

    # 确保端口释放
    local remaining_pids=$(lsof -ti:$TEST_SERVER_PORT 2>/dev/null || true)
    if [ -n "$remaining_pids" ]; then
        log_warning "强制停止占用端口 $TEST_SERVER_PORT 的进程：$remaining_pids"
        kill -9 $remaining_pids 2>/dev/null || true
    fi
}

# 运行集成测试
run_integration_tests() {
    log_step "9" "运行集成测试"

    local start_time=$(date +%s)

    # 启动测试服务器
    if ! start_test_server; then
        log_error "无法启动测试服务器，跳过集成测试"
        return 1
    fi

    # 运行端到端测试
    log_info "运行端到端测试..."
    local integration_success=true

    if timeout "$TEST_TIMEOUT" mvn test -pl file-transfer-demo \
        -Dtest=EndToEndTransferTest \
        -Dserver.port=$TEST_SERVER_PORT \
        -Dmaven.compiler.source=1.8 \
        -Dmaven.compiler.target=1.8 \
        >> "$MAIN_LOG" 2>&1; then

        log_success "端到端测试通过"
    else
        log_error "端到端测试失败"
        integration_success=false
    fi

    # 停止测试服务器
    stop_test_server

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    log_info "集成测试耗时：${duration}秒"

    if [ "$integration_success" = true ]; then
        log_success "集成测试通过"
        return 0
    else
        log_error "集成测试失败"
        return 1
    fi
}

# 运行性能测试
run_performance_tests() {
    log_step "10" "运行性能测试"

    local start_time=$(date +%s)

    log_info "运行性能测试..."

    # 运行服务端SDK性能测试
    if timeout "$TEST_TIMEOUT" mvn test -pl file-transfer-server-sdk \
        -Dtest=FileTransferPerformanceTest \
        -Dmaven.compiler.source=1.8 \
        -Dmaven.compiler.target=1.8 \
        >> "$MAIN_LOG" 2>&1; then

        log_success "性能测试通过"
        local performance_success=true
    else
        log_warning "性能测试未通过或未达到预期性能指标"
        local performance_success=false
    fi

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    log_info "性能测试耗时：${duration}秒"

    if [ "$performance_success" = true ]; then
        log_success "性能测试通过"
        return 0
    else
        log_warning "性能测试未完全通过，但不影响整体测试结果"
        return 1
    fi
}

# 生成测试覆盖率报告
generate_coverage_report() {
    log_step "11" "生成测试覆盖率报告"

    log_info "生成JaCoCo测试覆盖率报告..."

    # 检查是否配置了JaCoCo插件
    if grep -q "jacoco-maven-plugin" pom.xml; then
        log_info "检测到JaCoCo插件配置，生成覆盖率报告..."

        # 尝试生成覆盖率报告
        if mvn jacoco:report -q >> "$MAIN_LOG" 2>&1; then
            log_success "覆盖率报告生成成功"

            # 复制覆盖率报告到统一目录
            for module in "${PROJECT_MODULES[@]}"; do
                if [ -d "$module/target/site/jacoco" ]; then
                    local module_coverage_dir="$COVERAGE_REPORT_DIR/$module"
                    mkdir -p "$module_coverage_dir"
                    cp -r "$module/target/site/jacoco/"* "$module_coverage_dir/" 2>/dev/null || true
                    log_info "覆盖率报告已复制：$module_coverage_dir"
                fi
            done

            return 0
        else
            log_warning "JaCoCo覆盖率报告生成失败"
            return 1
        fi
    else
        log_info "未检测到JaCoCo插件配置，跳过覆盖率报告生成"
        log_info "如需生成覆盖率报告，请在父POM中配置JaCoCo插件"
        return 0
    fi
}

# ==================== 报告生成函数 ====================

# 收集测试结果
collect_test_results() {
    local total_tests=0
    local failed_tests=0
    local skipped_tests=0

    # 收集各模块的测试结果
    for module in "${PROJECT_MODULES[@]}"; do
        if [ ! -d "$module/target/surefire-reports" ]; then
            continue
        fi

        # 统计测试文件数量
        local module_test_files=$(find "$module/target/surefire-reports" -name "TEST-*.xml" | wc -l)

        if [ "$module_test_files" -gt 0 ]; then
            # 解析测试结果XML文件
            local module_tests=0
            local module_failures=0
            local module_errors=0
            local module_skipped=0

            for xml_file in "$module/target/surefire-reports/TEST-"*.xml; do
                if [ -f "$xml_file" ]; then
                    # 使用grep和awk解析XML属性
                    local test_info=$(grep '<testsuite' "$xml_file" | head -n 1)
                    if [ -n "$test_info" ]; then
                        local tests=$(echo "$test_info" | sed -n 's/.*tests="\([^"]*\)".*/\1/p')
                        local failures=$(echo "$test_info" | sed -n 's/.*failures="\([^"]*\)".*/\1/p')
                        local errors=$(echo "$test_info" | sed -n 's/.*errors="\([^"]*\)".*/\1/p')
                        local skipped=$(echo "$test_info" | sed -n 's/.*skipped="\([^"]*\)".*/\1/p')

                        module_tests=$((module_tests + ${tests:-0}))
                        module_failures=$((module_failures + ${failures:-0}))
                        module_errors=$((module_errors + ${errors:-0}))
                        module_skipped=$((module_skipped + ${skipped:-0}))
                    fi
                fi
            done

            local module_failed=$((module_failures + module_errors))

            total_tests=$((total_tests + module_tests))
            failed_tests=$((failed_tests + module_failed))
            skipped_tests=$((skipped_tests + module_skipped))

            log_info "$module: $module_tests 个测试，$module_failed 个失败，$module_skipped 个跳过"
        else
            log_warning "$module: 未找到测试结果文件"
        fi
    done

    local passed_tests=$((total_tests - failed_tests - skipped_tests))

    # 输出测试结果汇总
    echo ""
    echo "=========================================="
    echo "           测试结果汇总"
    echo "=========================================="
    echo "总测试数: $total_tests"
    echo "通过测试: $passed_tests"
    echo "失败测试: $failed_tests"
    echo "跳过测试: $skipped_tests"

    if [ "$failed_tests" -eq 0 ]; then
        echo -e "测试结果: ${COLOR_GREEN}全部通过${COLOR_NC}"
        return 0
    else
        echo -e "测试结果: ${COLOR_RED}有失败${COLOR_NC}"
        return 1
    fi
}

# 生成最终报告
generate_final_report() {
    log_step "12" "生成最终报告"

    local report_file="$LOG_DIR/final-report-$(date +%Y%m%d_%H%M%S).txt"

    {
        echo "========================================================"
        echo "        文件传输SDK构建和测试最终报告"
        echo "========================================================"
        echo "执行时间：$(date '+%Y-%m-%d %H:%M:%S')"
        echo "脚本版本：$SCRIPT_VERSION"
        echo "Java版本：$(java -version 2>&1 | head -n 1)"
        echo "Maven版本：$(mvn -version | head -n 1)"
        echo ""

        echo "执行模式："
        if [ "$BUILD_ONLY" = true ]; then
            echo "  构建模式：仅编译项目"
        else
            echo "  完整模式：构建 + 测试"
        fi
        echo ""

        echo "项目模块："
        for module in "${PROJECT_MODULES[@]}"; do
            if [ -d "$module" ]; then
                echo "  ✓ $module"
            else
                echo "  ✗ $module (目录不存在)"
            fi
        done
        echo ""

        echo "构建结果："
        for module in "${PROJECT_MODULES[@]}"; do
            if [ ! -d "$module" ]; then
                continue
            fi

            if [ -d "$module/target/classes" ]; then
                local class_count=$(find "$module/target/classes" -name "*.class" | wc -l)
                echo "  $module: 编译成功 ($class_count 个class文件)"
            else
                echo "  $module: 编译失败"
            fi
        done
        echo ""

        if [ "$BUILD_ONLY" = false ]; then
            echo "测试结果详情："
            for module in "${PROJECT_MODULES[@]}"; do
                if [ ! -d "$module/target/surefire-reports" ]; then
                    echo "  $module: 未运行测试"
                    continue
                fi

                local test_files=$(find "$module/target/surefire-reports" -name "TEST-*.xml" | wc -l)
                if [ "$test_files" -gt 0 ]; then
                    echo "  $module: $test_files 个测试套件"
                else
                    echo "  $module: 无测试结果"
                fi
            done
            echo ""

            echo "覆盖率报告："
            if [ -d "$COVERAGE_REPORT_DIR" ]; then
                for module in "${PROJECT_MODULES[@]}"; do
                    if [ -d "$COVERAGE_REPORT_DIR/$module" ]; then
                        echo "  $module: $COVERAGE_REPORT_DIR/$module/index.html"
                    fi
                done
            else
                echo "  未生成覆盖率报告"
            fi
            echo ""
        fi

        echo "详细日志：$MAIN_LOG"
        echo "========================================================"

    } > "$report_file"

    log_info "最终报告已生成：$report_file"

    # 显示报告内容
    cat "$report_file"

    return 0
}

# ==================== 清理和退出函数 ====================

# 清理函数（脚本退出时调用）
cleanup_on_exit() {
    local exit_code=$?

    # 停止测试服务器
    stop_test_server

    if [ $exit_code -ne 0 ]; then
        log_error "执行过程中发生错误，退出码：$exit_code"
        log_info "详细错误信息请查看日志文件：$MAIN_LOG"
    fi

    # 恢复原始环境变量
    if [ -n "$ORIGINAL_JAVA_HOME" ]; then
        export JAVA_HOME="$ORIGINAL_JAVA_HOME"
    fi

    if [ -n "$ORIGINAL_PATH" ]; then
        export PATH="$ORIGINAL_PATH"
    fi
}

# ==================== 帮助信息 ====================

# 显示帮助信息
show_help() {
    echo "========================================================"
    echo "    $SCRIPT_NAME"
    echo "    版本：$SCRIPT_VERSION"
    echo "========================================================"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "执行模式选项："
    echo "  --build-only          仅执行构建（编译+安装），不运行测试"
    echo "  --build-and-test      执行完整流程（构建+测试）[默认]"
    echo ""
    echo "Java环境选项："
    echo "  --java-home PATH      指定Java JDK路径"
    echo "  --use-default-java    使用系统默认Java（忽略脚本中的默认Java 8路径）"
    echo ""
    echo "测试控制选项（仅在--build-and-test模式下有效）："
    echo "  --unit-only           仅运行单元测试"
    echo "  --integration-only    仅运行集成测试"
    echo "  --performance         包含性能测试"
    echo "  --skip-coverage       跳过覆盖率报告生成"
    echo ""
    echo "执行控制选项："
    echo "  --no-cleanup          执行前不清理环境"
    echo "  --no-report           不生成最终报告"
    echo "  --verbose             显示详细输出"
    echo "  --help                显示此帮助信息"
    echo ""
    echo "环境变量："
    echo "  JAVA_HOME             可通过环境变量指定Java路径"
    echo "  MAVEN_OPTS            Maven执行选项（脚本会自动设置）"
    echo ""
    echo "使用示例："
    echo "  $0                              # 完整构建和测试流程"
    echo "  $0 --build-only                 # 仅构建项目"
    echo "  $0 --java-home /path/to/java    # 使用指定的Java路径"
    echo "  $0 --unit-only                  # 构建后仅运行单元测试"
    echo "  $0 --performance                # 包含性能测试的完整流程"
    echo "  $0 --verbose                    # 显示详细执行过程"
    echo ""
    echo "默认配置："
    echo "  Java路径: $DEFAULT_JAVA8_HOME"
    echo "  测试端口: $TEST_SERVER_PORT"
    echo "  日志目录: $LOG_DIR"
    echo ""
}

# ==================== 主程序 ====================

# 主函数
main() {
    # 保存原始环境变量
    export ORIGINAL_JAVA_HOME="$JAVA_HOME"
    export ORIGINAL_PATH="$PATH"

    # 设置退出时清理
    trap cleanup_on_exit EXIT

    # 解析命令行参数
    local custom_java_home=""
    local use_default_java=false
    BUILD_ONLY=false
    local unit_only=false
    local integration_only=false
    local run_performance=false
    local skip_coverage=false
    local no_cleanup=false
    local no_report=false
    local verbose=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            --build-only)
                BUILD_ONLY=true
                shift
                ;;
            --build-and-test)
                BUILD_ONLY=false
                shift
                ;;
            --java-home)
                custom_java_home="$2"
                shift 2
                ;;
            --use-default-java)
                use_default_java=true
                shift
                ;;
            --unit-only)
                unit_only=true
                integration_only=false
                shift
                ;;
            --integration-only)
                integration_only=true
                unit_only=false
                shift
                ;;
            --performance)
                run_performance=true
                shift
                ;;
            --skip-coverage)
                skip_coverage=true
                shift
                ;;
            --no-cleanup)
                no_cleanup=true
                shift
                ;;
            --no-report)
                no_report=true
                shift
                ;;
            --verbose)
                verbose=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                echo ""
                show_help
                exit 1
                ;;
        esac
    done

    # 显示脚本头部信息
    show_header

    # 初始化日志
    init_logging

    # 如果启用详细模式，设置bash调试
    if [ "$verbose" = true ]; then
        set -x
        log_info "启用详细输出模式"
    fi

    # 执行主要流程
    local execution_failed=false

    # 步骤1-4：环境检查和准备
    if [ "$use_default_java" = true ]; then
        if ! setup_java_environment ""; then
            execution_failed=true
        fi
    else
        if ! setup_java_environment "$custom_java_home"; then
            execution_failed=true
        fi
    fi

    if [ "$execution_failed" = false ] && ! check_maven_environment; then
        execution_failed=true
    elif [ "$execution_failed" = false ] && ! validate_project_structure; then
        execution_failed=true
    elif [ "$execution_failed" = false ] && [ "$no_cleanup" = false ] && ! clean_environment; then
        execution_failed=true
    # 步骤5-7：构建流程
    elif [ "$execution_failed" = false ] && ! compile_project; then
        execution_failed=true
    elif [ "$execution_failed" = false ] && ! install_project; then
        execution_failed=true
    elif [ "$execution_failed" = false ] && ! verify_build_results; then
        execution_failed=true
    fi

    # 步骤8-11：测试流程（仅在非build-only模式下执行）
    if [ "$execution_failed" = false ] && [ "$BUILD_ONLY" = false ]; then
        if [ "$integration_only" = false ] && ! run_unit_tests; then
            execution_failed=true
        elif [ "$unit_only" = false ] && ! run_integration_tests; then
            execution_failed=true
        elif [ "$run_performance" = true ] && ! run_performance_tests; then
            log_warning "性能测试未通过，但不影响整体执行结果"
        fi

        # 生成覆盖率报告
        if [ "$skip_coverage" = false ]; then
            generate_coverage_report
        fi

        # 收集测试结果
        if ! collect_test_results; then
            execution_failed=true
        fi
    fi

    # 步骤12：生成最终报告
    if [ "$no_report" = false ]; then
        generate_final_report
    fi

    # 返回结果
    if [ "$execution_failed" = true ]; then
        log_error "执行失败"
        exit 1
    else
        if [ "$BUILD_ONLY" = true ]; then
            log_success "构建成功完成"
        else
            log_success "构建和测试成功完成"
        fi
        exit 0
    fi
}

# 执行主函数
main "$@" 