package com.sdesrd.filetransfer.client.util;

import static org.junit.jupiter.api.Assertions.*;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicInteger;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import lombok.extern.slf4j.Slf4j;

/**
 * 重试管理器测试
 */
@Slf4j
@DisplayName("重试管理器测试")
class RetryManagerTest {
    
    @Test
    @DisplayName("测试成功执行（无需重试）")
    void testSuccessfulExecution() throws Exception {
        log.info("[测试开始] 测试成功执行（无需重试）");
        
        AtomicInteger callCount = new AtomicInteger(0);
        
        String result = RetryManager.executeWithRetry(
                () -> {
                    int count = callCount.incrementAndGet();
                    log.info("执行第{}次调用", count);
                    return "成功结果";
                },
                RetryManager.RetryPolicy.defaultPolicy(),
                "测试成功操作"
        );
        
        assertEquals("成功结果", result, "应该返回正确结果");
        assertEquals(1, callCount.get(), "成功情况下应该只调用1次");
        
        log.info("[测试完成] 成功执行测试通过");
    }
    
    @Test
    @DisplayName("测试重试成功")
    void testRetrySuccess() throws Exception {
        log.info("[测试开始] 测试重试成功");
        
        AtomicInteger callCount = new AtomicInteger(0);
        
        String result = RetryManager.executeWithRetry(
                () -> {
                    int count = callCount.incrementAndGet();
                    log.info("执行第{}次调用", count);
                    
                    if (count < 3) {
                        throw new RuntimeException("模拟失败 - 第" + count + "次");
                    }
                    return "重试成功";
                },
                RetryManager.RetryPolicy.defaultPolicy(),
                "测试重试操作"
        );
        
        assertEquals("重试成功", result, "应该返回正确结果");
        assertEquals(3, callCount.get(), "应该调用3次（2次失败 + 1次成功）");
        
        log.info("[测试完成] 重试成功测试通过");
    }
    
    @Test
    @DisplayName("测试重试失败")
    void testRetryFailure() {
        log.info("[测试开始] 测试重试失败");
        
        AtomicInteger callCount = new AtomicInteger(0);
        
        Exception exception = assertThrows(RuntimeException.class, () -> {
            RetryManager.executeWithRetry(
                    () -> {
                        int count = callCount.incrementAndGet();
                        log.info("执行第{}次调用", count);
                        throw new RuntimeException("持续失败 - 第" + count + "次");
                    },
                    RetryManager.RetryPolicy.defaultPolicy(),
                    "测试失败操作"
            );
        });
        
        assertTrue(exception.getMessage().contains("重试失败"), "应该抛出重试失败异常");
        assertEquals(4, callCount.get(), "应该调用4次（1次初始 + 3次重试）");
        
        log.info("[测试完成] 重试失败测试通过 - 异常: {}", exception.getMessage());
    }
    
    @Test
    @DisplayName("测试快速重试策略")
    void testQuickRetryPolicy() throws Exception {
        log.info("[测试开始] 测试快速重试策略");
        
        AtomicInteger callCount = new AtomicInteger(0);
        long startTime = System.currentTimeMillis();
        
        String result = RetryManager.executeWithRetry(
                () -> {
                    int count = callCount.incrementAndGet();
                    log.info("执行第{}次调用", count);
                    
                    if (count < 2) {
                        throw new RuntimeException("模拟失败");
                    }
                    return "快速重试成功";
                },
                RetryManager.quickRetry().build(),
                "测试快速重试"
        );
        
        long duration = System.currentTimeMillis() - startTime;
        
        assertEquals("快速重试成功", result, "应该返回正确结果");
        assertEquals(2, callCount.get(), "应该调用2次");
        assertTrue(duration < 1000, "快速重试应该在1秒内完成，实际耗时: " + duration + "ms");
        
        log.info("[测试完成] 快速重试策略测试通过 - 耗时: {}ms", duration);
    }
    
    @Test
    @DisplayName("测试网络重试策略")
    void testNetworkRetryPolicy() throws Exception {
        log.info("[测试开始] 测试网络重试策略");
        
        AtomicInteger callCount = new AtomicInteger(0);
        
        String result = RetryManager.executeWithRetry(
                () -> {
                    int count = callCount.incrementAndGet();
                    log.info("执行第{}次调用", count);
                    
                    if (count < 3) {
                        throw new IOException("模拟网络异常");
                    }
                    return "网络重试成功";
                },
                RetryManager.networkRetry().build(),
                "测试网络重试"
        );
        
        assertEquals("网络重试成功", result, "应该返回正确结果");
        assertEquals(3, callCount.get(), "应该调用3次");
        
        log.info("[测试完成] 网络重试策略测试通过");
    }
    
    @Test
    @DisplayName("测试自定义重试策略")
    void testCustomRetryPolicy() throws Exception {
        log.info("[测试开始] 测试自定义重试策略");
        
        AtomicInteger callCount = new AtomicInteger(0);
        
        // 创建自定义重试策略：最多重试1次，基础延迟50ms
        RetryManager.RetryPolicy customPolicy = new RetryManager.RetryPolicyBuilder()
                .maxRetries(1)
                .baseDelay(50L)
                .maxDelay(200L)
                .backoffMultiplier(2.0)
                .jitterFactor(0.0) // 无抖动，便于测试
                .build();
        
        long startTime = System.currentTimeMillis();
        
        String result = RetryManager.executeWithRetry(
                () -> {
                    int count = callCount.incrementAndGet();
                    log.info("执行第{}次调用", count);
                    
                    if (count < 2) {
                        throw new RuntimeException("模拟失败");
                    }
                    return "自定义重试成功";
                },
                customPolicy,
                "测试自定义重试"
        );
        
        long duration = System.currentTimeMillis() - startTime;
        
        assertEquals("自定义重试成功", result, "应该返回正确结果");
        assertEquals(2, callCount.get(), "应该调用2次");
        assertTrue(duration >= 50, "应该有延迟时间，实际耗时: " + duration + "ms");
        assertTrue(duration < 500, "延迟时间应该合理，实际耗时: " + duration + "ms");
        
        log.info("[测试完成] 自定义重试策略测试通过 - 耗时: {}ms", duration);
    }
    
    @Test
    @DisplayName("测试可重试异常过滤")
    void testRetryableExceptionFilter() {
        log.info("[测试开始] 测试可重试异常过滤");
        
        AtomicInteger callCount = new AtomicInteger(0);
        
        // 创建只重试IOException的策略
        RetryManager.RetryPolicy policy = new RetryManager.RetryPolicyBuilder()
                .maxRetries(2)
                .baseDelay(10L)
                .retryOn(IOException.class)
                .build();
        
        // 测试可重试异常
        log.info("[测试步骤] 测试可重试异常（IOException）");
        callCount.set(0);
        
        Exception ioException = assertThrows(RuntimeException.class, () -> {
            RetryManager.executeWithRetry(
                    () -> {
                        callCount.incrementAndGet();
                        throw new IOException("网络异常");
                    },
                    policy,
                    "测试IO异常"
            );
        });
        
        assertEquals(3, callCount.get(), "IOException应该重试，总共调用3次");
        
        // 测试不可重试异常
        log.info("[测试步骤] 测试不可重试异常（IllegalArgumentException）");
        callCount.set(0);
        
        Exception illegalException = assertThrows(IllegalArgumentException.class, () -> {
            RetryManager.executeWithRetry(
                    () -> {
                        callCount.incrementAndGet();
                        throw new IllegalArgumentException("参数异常");
                    },
                    policy,
                    "测试参数异常"
            );
        });
        
        assertEquals(1, callCount.get(), "IllegalArgumentException不应该重试，只调用1次");
        
        log.info("[测试完成] 可重试异常过滤测试通过");
    }
    
    @Test
    @DisplayName("测试无返回值操作重试")
    void testVoidOperationRetry() throws Exception {
        log.info("[测试开始] 测试无返回值操作重试");
        
        AtomicInteger callCount = new AtomicInteger(0);
        AtomicInteger successCount = new AtomicInteger(0);
        
        RetryManager.executeWithRetry(
                () -> {
                    int count = callCount.incrementAndGet();
                    log.info("执行第{}次调用", count);
                    
                    if (count < 3) {
                        throw new RuntimeException("模拟失败");
                    }
                    
                    successCount.incrementAndGet();
                    log.info("操作成功执行");
                },
                RetryManager.RetryPolicy.defaultPolicy(),
                "测试无返回值操作"
        );
        
        assertEquals(3, callCount.get(), "应该调用3次");
        assertEquals(1, successCount.get(), "应该成功执行1次");
        
        log.info("[测试完成] 无返回值操作重试测试通过");
    }
    
    @Test
    @DisplayName("测试指数退避延迟计算")
    void testExponentialBackoffDelay() throws Exception {
        log.info("[测试开始] 测试指数退避延迟计算");
        
        AtomicInteger callCount = new AtomicInteger(0);
        long[] callTimes = new long[4];
        
        // 创建有明确延迟的重试策略
        RetryManager.RetryPolicy policy = new RetryManager.RetryPolicyBuilder()
                .maxRetries(3)
                .baseDelay(100L)
                .backoffMultiplier(2.0)
                .jitterFactor(0.0) // 无抖动，便于测试
                .build();
        
        Exception exception = assertThrows(RuntimeException.class, () -> {
            RetryManager.executeWithRetry(
                    () -> {
                        int count = callCount.incrementAndGet();
                        callTimes[count - 1] = System.currentTimeMillis();
                        log.info("执行第{}次调用 - 时间: {}", count, callTimes[count - 1]);
                        throw new RuntimeException("持续失败");
                    },
                    policy,
                    "测试指数退避"
            );
        });
        
        assertEquals(4, callCount.get(), "应该调用4次");
        
        // 验证延迟时间（允许一定误差）
        long delay1 = callTimes[1] - callTimes[0]; // 第一次重试延迟
        long delay2 = callTimes[2] - callTimes[1]; // 第二次重试延迟
        long delay3 = callTimes[3] - callTimes[2]; // 第三次重试延迟
        
        log.info("延迟时间 - 第1次重试: {}ms, 第2次重试: {}ms, 第3次重试: {}ms", 
                delay1, delay2, delay3);
        
        // 验证指数退避（允许±50ms误差）
        assertTrue(Math.abs(delay1 - 100) <= 50, "第1次重试延迟应该约为100ms，实际: " + delay1 + "ms");
        assertTrue(Math.abs(delay2 - 200) <= 50, "第2次重试延迟应该约为200ms，实际: " + delay2 + "ms");
        assertTrue(Math.abs(delay3 - 400) <= 50, "第3次重试延迟应该约为400ms，实际: " + delay3 + "ms");
        
        log.info("[测试完成] 指数退避延迟计算测试通过");
    }
}
