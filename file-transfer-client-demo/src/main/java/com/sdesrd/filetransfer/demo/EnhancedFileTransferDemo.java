package com.sdesrd.filetransfer.demo;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Scanner;
import java.util.concurrent.CompletableFuture;

import com.sdesrd.filetransfer.client.FileTransferClient;
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.config.ClientConfigBuilder;
import com.sdesrd.filetransfer.client.dto.DownloadResult;
import com.sdesrd.filetransfer.client.dto.TransferProgress;
import com.sdesrd.filetransfer.client.dto.UploadResult;
import com.sdesrd.filetransfer.client.listener.TransferListener;
import com.sdesrd.filetransfer.client.util.ConcurrentTransferManager;
import com.sdesrd.filetransfer.client.util.FileUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 增强的文件传输演示应用
 * 展示新增的重试机制、并发控制和性能监控功能
 */
@Slf4j
public class EnhancedFileTransferDemo {
    
    /** 演示文件目录 */
    private static final String DEMO_DIR = "demo-files";
    private static final String UPLOAD_DIR = DEMO_DIR + "/upload";
    private static final String DOWNLOAD_DIR = DEMO_DIR + "/download";
    
    /** 演示用的传输监听器 */
    private static final TransferListener DEMO_LISTENER = new DemoTransferListener();
    
    private static FileTransferClient client;
    private static Scanner scanner;
    
    public static void main(String[] args) {
        log.info("=== 增强的文件传输SDK演示 ===");
        
        try {
            // 初始化
            initializeDemo();
            
            // 显示主菜单
            showMainMenu();
            
        } catch (Exception e) {
            log.error("演示程序运行失败", e);
        } finally {
            cleanup();
        }
    }
    
    /**
     * 初始化演示环境
     */
    private static void initializeDemo() throws IOException {
        log.info("初始化演示环境...");
        
        // 创建演示目录
        createDemoDirectories();
        
        // 创建增强的客户端配置
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr("localhost")
                .serverPort(49011)
                .auth("demo", "demo-secret-key-2024")
                .chunkSize(1024 * 1024) // 1MB 分块
                .maxConcurrentTransfers(3) // 最大3个并发传输
                .retry(3, 1000) // 重试3次，基础延迟1秒
                .timeout(30, 60, 60) // 连接、读取、写入超时
                .build();
        
        // 创建客户端
        client = new FileTransferClient(config);
        scanner = new Scanner(System.in);
        
        log.info("演示环境初始化完成");
        log.info("  - 服务器地址: {}:{}", config.getServerAddr(), config.getServerPort());
        log.info("  - 分块大小: {}", FileUtils.formatFileSize(config.getChunkSize()));
        log.info("  - 最大并发数: {}", config.getMaxConcurrentTransfers());
        log.info("  - 重试次数: {}", config.getRetryCount());
    }
    
    /**
     * 创建演示目录
     */
    private static void createDemoDirectories() throws IOException {
        Path uploadPath = Paths.get(UPLOAD_DIR);
        Path downloadPath = Paths.get(DOWNLOAD_DIR);
        
        Files.createDirectories(uploadPath);
        Files.createDirectories(downloadPath);
        
        // 创建一些演示文件
        createDemoFiles();
    }
    
    /**
     * 创建演示文件
     */
    private static void createDemoFiles() throws IOException {
        // 小文件
        createDemoFile("small-file.txt", "这是一个小文件的内容。\n".repeat(100));
        
        // 中等文件
        createDemoFile("medium-file.txt", "这是一个中等大小文件的内容。\n".repeat(5000));
        
        // 大文件
        createDemoFile("large-file.txt", "这是一个大文件的内容，用于测试分片传输。\n".repeat(50000));
        
        log.info("演示文件创建完成");
    }
    
    /**
     * 创建演示文件
     */
    private static void createDemoFile(String fileName, String content) throws IOException {
        Path filePath = Paths.get(UPLOAD_DIR, fileName);
        if (!Files.exists(filePath)) {
            Files.writeString(filePath, content);
            log.debug("创建演示文件: {} ({})", fileName, FileUtils.formatFileSize(content.length()));
        }
    }
    
    /**
     * 显示主菜单
     */
    private static void showMainMenu() {
        while (true) {
            System.out.println("\n=== 增强的文件传输SDK演示菜单 ===");
            System.out.println("1. 单文件上传演示");
            System.out.println("2. 并发上传演示");
            System.out.println("3. 增强分片下载演示");
            System.out.println("4. 断点续传演示");
            System.out.println("5. 传输统计查看");
            System.out.println("6. 性能压力测试");
            System.out.println("7. 重试机制演示");
            System.out.println("0. 退出");
            System.out.print("请选择功能 (0-7): ");
            
            try {
                int choice = Integer.parseInt(scanner.nextLine().trim());
                
                switch (choice) {
                    case 1:
                        demonstrateSingleUpload();
                        break;
                    case 2:
                        demonstrateConcurrentUpload();
                        break;
                    case 3:
                        demonstrateEnhancedChunkedDownload();
                        break;
                    case 4:
                        demonstrateResumeDownload();
                        break;
                    case 5:
                        showTransferStatistics();
                        break;
                    case 6:
                        demonstratePerformanceTest();
                        break;
                    case 7:
                        demonstrateRetryMechanism();
                        break;
                    case 0:
                        System.out.println("感谢使用增强的文件传输SDK演示！");
                        return;
                    default:
                        System.out.println("无效选择，请重新输入。");
                }
            } catch (NumberFormatException e) {
                System.out.println("请输入有效的数字。");
            } catch (Exception e) {
                log.error("执行演示功能失败", e);
                System.out.println("执行失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 演示单文件上传
     */
    private static void demonstrateSingleUpload() throws Exception {
        System.out.println("\n=== 单文件上传演示 ===");
        
        // 列出可上传的文件
        File uploadDir = new File(UPLOAD_DIR);
        File[] files = uploadDir.listFiles();
        
        if (files == null || files.length == 0) {
            System.out.println("没有可上传的文件。");
            return;
        }
        
        System.out.println("可上传的文件:");
        for (int i = 0; i < files.length; i++) {
            System.out.printf("%d. %s (%s)\n", i + 1, files[i].getName(), 
                    FileUtils.formatFileSize(files[i].length()));
        }
        
        System.out.print("请选择要上传的文件 (1-" + files.length + "): ");
        int fileIndex = Integer.parseInt(scanner.nextLine().trim()) - 1;
        
        if (fileIndex < 0 || fileIndex >= files.length) {
            System.out.println("无效的文件选择。");
            return;
        }
        
        File selectedFile = files[fileIndex];
        System.out.println("开始上传文件: " + selectedFile.getName());
        
        // 执行上传
        CompletableFuture<UploadResult> future = client.uploadFile(
                selectedFile.getAbsolutePath(), null, DEMO_LISTENER);
        
        UploadResult result = future.get();
        
        if (result.isSuccess()) {
            System.out.println("✅ 上传成功!");
            System.out.println("   文件ID: " + result.getFileId());
            System.out.println("   文件大小: " + FileUtils.formatFileSize(result.getFileSize()));
        } else {
            System.out.println("❌ 上传失败: " + result.getErrorMessage());
        }
    }
    
    /**
     * 演示并发上传
     */
    private static void demonstrateConcurrentUpload() throws Exception {
        System.out.println("\n=== 并发上传演示 ===");
        
        File uploadDir = new File(UPLOAD_DIR);
        File[] files = uploadDir.listFiles();
        
        if (files == null || files.length < 2) {
            System.out.println("需要至少2个文件进行并发上传演示。");
            return;
        }
        
        System.out.println("将并发上传以下文件:");
        for (int i = 0; i < Math.min(3, files.length); i++) {
            System.out.printf("  %s (%s)\n", files[i].getName(), 
                    FileUtils.formatFileSize(files[i].length()));
        }
        
        System.out.print("按回车键开始并发上传...");
        scanner.nextLine();
        
        // 启动并发上传
        CompletableFuture<UploadResult>[] futures = new CompletableFuture[Math.min(3, files.length)];
        
        for (int i = 0; i < futures.length; i++) {
            futures[i] = client.uploadFile(files[i].getAbsolutePath(), null, DEMO_LISTENER);
        }
        
        // 等待所有上传完成
        System.out.println("等待所有上传完成...");
        CompletableFuture.allOf(futures).get();
        
        // 显示结果
        System.out.println("\n并发上传结果:");
        for (int i = 0; i < futures.length; i++) {
            UploadResult result = futures[i].get();
            if (result.isSuccess()) {
                System.out.printf("✅ %s - 文件ID: %s\n", files[i].getName(), result.getFileId());
            } else {
                System.out.printf("❌ %s - 失败: %s\n", files[i].getName(), result.getErrorMessage());
            }
        }
    }
    
    /**
     * 演示增强分片下载
     */
    private static void demonstrateEnhancedChunkedDownload() throws Exception {
        System.out.println("\n=== 增强分片下载演示 ===");
        
        // 先上传一个文件用于下载
        File uploadFile = new File(UPLOAD_DIR, "large-file.txt");
        if (!uploadFile.exists()) {
            System.out.println("演示文件不存在，请先运行单文件上传。");
            return;
        }
        
        System.out.println("上传文件用于下载演示...");
        UploadResult uploadResult = client.uploadFile(uploadFile.getAbsolutePath(), null, DEMO_LISTENER).get();
        
        if (!uploadResult.isSuccess()) {
            System.out.println("上传失败: " + uploadResult.getErrorMessage());
            return;
        }
        
        // 执行增强分片下载
        String downloadPath = DOWNLOAD_DIR + "/enhanced-download-" + uploadFile.getName();
        System.out.println("开始增强分片下载...");
        
        DownloadResult downloadResult = client.downloadFileChunk(
                uploadResult.getFileId(), downloadPath, DEMO_LISTENER).get();
        
        if (downloadResult.isSuccess()) {
            System.out.println("✅ 增强分片下载成功!");
            System.out.println("   保存路径: " + downloadPath);
            System.out.println("   文件大小: " + FileUtils.formatFileSize(downloadResult.getFileSize()));
        } else {
            System.out.println("❌ 下载失败: " + downloadResult.getErrorMessage());
        }
    }
    
    /**
     * 显示传输统计
     */
    private static void showTransferStatistics() {
        System.out.println("\n=== 传输统计信息 ===");
        
        ConcurrentTransferManager.TransferStats stats = client.getTransferStats();
        System.out.println(stats.toString());
        
        System.out.println("\n详细统计:");
        System.out.printf("  活跃上传: %d\n", stats.getActiveUploads());
        System.out.printf("  活跃下载: %d\n", stats.getActiveDownloads());
        System.out.printf("  总上传字节: %s\n", FileUtils.formatFileSize(stats.getTotalUploadedBytes()));
        System.out.printf("  总下载字节: %s\n", FileUtils.formatFileSize(stats.getTotalDownloadedBytes()));
        System.out.printf("  活跃传输: %d/%d\n", stats.getActiveTransfers(), stats.getMaxConcurrentTransfers());
        System.out.printf("  可用槽位: %s\n", client.hasAvailableSlot() ? "是" : "否");
    }
    
    /**
     * 演示断点续传（简化版）
     */
    private static void demonstrateResumeDownload() {
        System.out.println("\n=== 断点续传演示 ===");
        System.out.println("断点续传功能已集成到增强分片下载中。");
        System.out.println("当下载中断时，重新下载会自动从断点位置继续。");
        System.out.println("请参考增强分片下载演示。");
    }
    
    /**
     * 演示性能压力测试（简化版）
     */
    private static void demonstratePerformanceTest() {
        System.out.println("\n=== 性能压力测试演示 ===");
        System.out.println("性能测试功能已集成到传输统计中。");
        System.out.println("请执行多次传输操作后查看传输统计信息。");
    }
    
    /**
     * 演示重试机制（简化版）
     */
    private static void demonstrateRetryMechanism() {
        System.out.println("\n=== 重试机制演示 ===");
        System.out.println("重试机制已自动集成到所有传输操作中。");
        System.out.println("当传输失败时，系统会自动重试最多3次。");
        System.out.println("重试采用指数退避策略，提高成功率。");
    }
    
    /**
     * 清理资源
     */
    private static void cleanup() {
        if (client != null) {
            client.close();
        }
        if (scanner != null) {
            scanner.close();
        }
        log.info("演示程序资源清理完成");
    }
    
    /**
     * 演示传输监听器
     */
    private static class DemoTransferListener implements TransferListener {
        
        @Override
        public void onStart(TransferProgress progress) {
            System.out.printf("🚀 开始传输: %s\n", progress.getFileName());
        }
        
        @Override
        public void onProgress(TransferProgress progress) {
            // 每10%进度输出一次
            if (progress.getProgress() % 10 < 1) {
                System.out.printf("📊 传输进度: %.1f%% - %s/%s\n", 
                        progress.getProgress(),
                        FileUtils.formatFileSize(progress.getTransferredSize()),
                        FileUtils.formatFileSize(progress.getTotalSize()));
            }
        }
        
        @Override
        public void onCompleted(TransferProgress progress) {
            System.out.printf("✅ 传输完成: %s (%s)\n", 
                    progress.getFileName(),
                    FileUtils.formatFileSize(progress.getTotalSize()));
        }
        
        @Override
        public void onError(TransferProgress progress, Throwable error) {
            System.out.printf("❌ 传输失败: %s - %s\n", 
                    progress.getFileName(), error.getMessage());
        }
    }
}
