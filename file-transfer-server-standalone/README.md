# 文件传输独立服务端

这是一个独立的文件传输服务端应用，可以作为单独的进程运行，提供完整的文件传输服务功能。

## 功能特性

- ✅ **独立运行**: 可作为独立进程运行，无需集成到其他应用
- ✅ **完整功能**: 包含所有文件传输SDK的功能
- ✅ **断点续传**: 支持文件上传下载中断后继续传输
- ✅ **传输限速**: 可配置上传下载速度限制
- ✅ **监控支持**: 内置健康检查和监控端点
- ✅ **配置灵活**: 支持外部配置文件和环境变量

## 快速开始

### 1. 编译项目

```bash
# 确保已经安装了file-transfer-server-sdk
cd ../file-transfer-server-sdk
mvn clean install

# 编译独立服务端
cd ../file-transfer-server-standalone
mvn clean package
```

### 2. 运行服务

```bash
# 直接运行jar包
java -jar target/file-transfer-server-standalone-1.0.0.jar

# 或者使用Maven运行
mvn spring-boot:run
```

### 3. 指定配置文件运行

```bash
# 使用自定义配置文件
java -jar target/file-transfer-server-standalone-1.0.0.jar --spring.config.location=classpath:/custom-config.yml

# 使用外部配置文件
java -jar target/file-transfer-server-standalone-1.0.0.jar --spring.config.location=file:./config/application.yml
```

### 4. 环境变量配置

```bash
# 设置端口
export SERVER_PORT=9090

# 设置存储路径
export FILE_TRANSFER_SERVER_STORAGE_PATH=/data/files

# 运行
java -jar target/file-transfer-server-standalone-1.0.0.jar
```

## 服务端点

启动后可访问以下端点：

- **API文档**: http://localhost:49011/doc.html
- **健康检查**: http://localhost:49011/actuator/health
- **文件上传**: http://localhost:49011/filetransfer/api/file/upload/init
- **文件下载**: http://localhost:49011/filetransfer/api/file/download/{fileId}

## 配置说明

### 基础配置

```yaml
server:
  port: 49011                    # 服务端口

file:
  transfer:
    server:
      enabled: true             # 启用文件传输服务
      storage-path: ./data      # 文件存储路径
      upload-rate-limit: 10485760  # 上传速度限制(10MB/s)
      download-rate-limit: 10485760 # 下载速度限制(10MB/s)
```

### 高级配置

```yaml
file:
  transfer:
    server:
      fast-upload-enabled: true    # 启用秒传
      rate-limit-enabled: true     # 启用限流
      max-file-size: 104857600     # 最大文件大小(100MB)
      default-chunk-size: 2097152  # 分块大小(2MB)
      cleanup-interval: 3600000    # 清理间隔(1小时)
```

## 部署方式

### 1. 直接部署

```bash
# 后台运行
nohup java -jar file-transfer-server-standalone-1.0.0.jar > server.log 2>&1 &
```

### 2. 系统服务部署

创建systemd服务文件 `/etc/systemd/system/file-transfer-server.service`：

```ini
[Unit]
Description=File Transfer Server
After=network.target

[Service]
Type=simple
User=filetransfer
ExecStart=/usr/bin/java -jar /opt/file-transfer/file-transfer-server-standalone-1.0.0.jar
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable file-transfer-server
sudo systemctl start file-transfer-server
```

### 3. Docker部署

创建Dockerfile：

```dockerfile
FROM openjdk:8-jre-alpine

WORKDIR /app
COPY target/file-transfer-server-standalone-1.0.0.jar app.jar

EXPOSE 49011
VOLUME ["/data"]

CMD ["java", "-jar", "app.jar"]
```

构建和运行：

```bash
docker build -t file-transfer-server .
docker run -d -p 49011:49011 -v /host/data:/data file-transfer-server
```

## 监控和维护

### 健康检查

```bash
# 检查服务状态
curl http://localhost:49011/actuator/health

# 查看详细信息
curl http://localhost:49011/actuator/info
```

### 日志查看

```bash
# 查看实时日志
tail -f logs/file-transfer-server.log

# 查看错误日志
grep ERROR logs/file-transfer-server.log
```

### 性能监控

访问 http://localhost:49011/actuator/metrics 查看性能指标

## 客户端连接

使用客户端SDK连接到独立服务端：

```java
ClientConfig config = new ClientConfig();
config.setServerUrl("http://your-server:49011/file-transfer");
FileTransferClient client = new FileTransferClient(config);
```

## 故障排除

### 常见问题

1. **端口占用**: 修改配置文件中的端口号
2. **权限问题**: 确保存储目录有写入权限
3. **内存不足**: 调整JVM内存参数 `-Xms512m -Xmx1024m`

### 调试模式

```bash
java -jar file-transfer-server-standalone-1.0.0.jar --logging.level.com.sdesrd.filetransfer=DEBUG
``` 