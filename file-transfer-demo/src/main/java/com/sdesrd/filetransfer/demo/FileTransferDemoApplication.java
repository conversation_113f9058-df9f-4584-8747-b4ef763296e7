package com.sdesrd.filetransfer.demo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 文件传输SDK演示应用
 * 
 * 这个应用演示了如何集成和使用文件传输服务端SDK
 */
@SpringBootApplication(scanBasePackages = {"com.sdesrd.filetransfer"})
public class FileTransferDemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(FileTransferDemoApplication.class, args);
        System.out.println("===========================================");
        System.out.println("文件传输SDK演示应用启动成功！");
        System.out.println("API文档地址: http://localhost:49011/doc.html");
        System.out.println("服务端点: http://localhost:49011/file-transfer");
        System.out.println("===========================================");
    }
} 