# ================================================================================
# 文件传输SDK统一构建和测试脚本 (PowerShell版本)
# 功能：集成构建、测试和环境配置的统一脚本
# 支持系统：Windows PowerShell 5.1+ 或 PowerShell Core 6.0+
# ================================================================================

[CmdletBinding()]
param(
    [switch]$BuildOnly,
    [switch]$BuildAndTest,
    [string]$JavaHome,
    [switch]$UseDefaultJava,
    [switch]$UnitOnly,
    [switch]$IntegrationOnly,
    [switch]$Performance,
    [switch]$SkipCoverage,
    [switch]$NoCleanup,
    [switch]$NoReport,
    [switch]$Verbose,
    [switch]$Help
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# ==================== 常量定义 ====================

# 脚本版本信息
$SCRIPT_VERSION = "2.0.0"
$SCRIPT_NAME = "文件传输SDK统一构建和测试脚本 (PowerShell版本)"

# 默认Java 8 JDK路径 (Windows适配)
$DEFAULT_JAVA8_HOME = "$env:USERPROFILE\.jdks\corretto-1.8.0_452"

# 项目模块列表
$PROJECT_MODULES = @(
    "file-transfer-server-sdk",
    "file-transfer-client-sdk", 
    "file-transfer-demo"
)

# 独立模块列表
$STANDALONE_MODULES = @(
    "file-transfer-server-standalone"
)

# 超时配置
$BUILD_TIMEOUT = 600    # 构建超时时间（秒）
$TEST_TIMEOUT = 1200    # 测试超时时间（秒）
$SERVER_STARTUP_TIMEOUT = 30  # 服务器启动超时时间（秒）

# 端口配置
$TEST_SERVER_PORT = 49012  # 测试服务器端口

# 目录配置
$LOG_DIR = ".\logs"
$TIMESTAMP = Get-Date -Format "yyyyMMdd_HHmmss"
$MAIN_LOG = "$LOG_DIR\build-and-test-$TIMESTAMP.log"
$COVERAGE_REPORT_DIR = "$LOG_DIR\coverage"

# ==================== 颜色定义 ====================

$COLOR_RED = "Red"
$COLOR_GREEN = "Green"
$COLOR_YELLOW = "Yellow"
$COLOR_BLUE = "Blue"
$COLOR_MAGENTA = "Magenta"
$COLOR_CYAN = "Cyan"

# ==================== 日志函数 ====================

function Write-Log {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Message,
        [Parameter(Mandatory=$true)]
        [string]$Level,
        [string]$Color = "White"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$Level] $timestamp - $Message"
    
    # 控制台输出（带颜色）
    Write-Host $logMessage -ForegroundColor $Color
    
    # 文件输出
    Add-Content -Path $MAIN_LOG -Value $logMessage -Encoding UTF8
}

function Log-Info {
    param([string]$Message)
    Write-Log -Message $Message -Level "INFO" -Color $COLOR_BLUE
}

function Log-Success {
    param([string]$Message)
    Write-Log -Message $Message -Level "SUCCESS" -Color $COLOR_GREEN
}

function Log-Warning {
    param([string]$Message)
    Write-Log -Message $Message -Level "WARNING" -Color $COLOR_YELLOW
}

function Log-Error {
    param([string]$Message)
    Write-Log -Message $Message -Level "ERROR" -Color $COLOR_RED
}

function Log-Step {
    param(
        [string]$StepNumber,
        [string]$StepName
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $message = "[STEP $StepNumber] $timestamp - $StepName"
    Write-Host $message -ForegroundColor $COLOR_MAGENTA
    Add-Content -Path $MAIN_LOG -Value $message -Encoding UTF8
}

# ==================== 工具函数 ====================

function Initialize-Logging {
    # 创建日志目录
    if (-not (Test-Path $LOG_DIR)) {
        New-Item -ItemType Directory -Path $LOG_DIR -Force | Out-Null
    }
    
    if (-not (Test-Path $COVERAGE_REPORT_DIR)) {
        New-Item -ItemType Directory -Path $COVERAGE_REPORT_DIR -Force | Out-Null
    }
    
    # 创建主日志文件
    if (-not (Test-Path $MAIN_LOG)) {
        New-Item -ItemType File -Path $MAIN_LOG -Force | Out-Null
    }
    
    Log-Info "主日志文件：$MAIN_LOG"
}

function Show-Header {
    Write-Host "========================================================"
    Write-Host "    $SCRIPT_NAME"
    Write-Host "    版本：$SCRIPT_VERSION"
    Write-Host "    时间：$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
    Write-Host "========================================================"
}

function Test-Command {
    param(
        [string]$Command,
        [string]$Description
    )
    
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        Log-Error "$Description 未安装或未在PATH中：$Command"
        return $false
    }
}

# ==================== 环境检查函数 ====================

function Setup-JavaEnvironment {
    param([string]$CustomJavaHome)
    
    Log-Step "1" "设置Java环境"
    
    # 保存原始环境变量
    $script:ORIGINAL_JAVA_HOME = $env:JAVA_HOME
    $script:ORIGINAL_PATH = $env:PATH
    
    # 如果指定了自定义Java路径，使用它
    if ($CustomJavaHome) {
        if ((Test-Path $CustomJavaHome) -and (Test-Path "$CustomJavaHome\bin\java.exe")) {
            $env:JAVA_HOME = $CustomJavaHome
            $env:PATH = "$CustomJavaHome\bin;$env:PATH"
            Log-Info "使用指定的Java JDK：$CustomJavaHome"
        }
        else {
            Log-Error "指定的Java JDK路径无效：$CustomJavaHome"
            return $false
        }
    }
    # 检查默认的Java 8 JDK是否存在
    elseif ((Test-Path $DEFAULT_JAVA8_HOME) -and (Test-Path "$DEFAULT_JAVA8_HOME\bin\java.exe")) {
        $env:JAVA_HOME = $DEFAULT_JAVA8_HOME
        $env:PATH = "$DEFAULT_JAVA8_HOME\bin;$env:PATH"
        Log-Info "使用默认的Java 8 JDK：$DEFAULT_JAVA8_HOME"
    }
    # 使用系统默认Java
    else {
        Log-Warning "未找到默认Java 8 JDK，使用系统默认Java"
        Log-Info "确保系统Java版本兼容项目要求"
    }
    
    # 验证Java命令可用性
    if (-not (Test-Command "java" "Java运行时")) {
        return $false
    }
    
    # 获取Java版本信息
    try {
        $javaVersion = & java -version 2>&1 | Select-Object -First 1
        $versionMatch = [regex]::Match($javaVersion, '"([^"]*)"')
        if ($versionMatch.Success) {
            $version = $versionMatch.Groups[1].Value
            Log-Info "当前Java版本：$version"
            
            # 验证Java版本兼容性
            if ($version -match "^1\.8\.") {
                Log-Success "使用Java 8，完全兼容"
            }
            elseif ($version -match "^(11|17|21)\.") {
                Log-Warning "使用Java $version，项目配置为Java 8，但应该向后兼容"
            }
            else {
                Log-Warning "当前Java版本：$version，可能存在兼容性问题"
            }
        }
    }
    catch {
        Log-Warning "无法获取Java版本信息"
    }
    
    return $true
}

function Test-MavenEnvironment {
    Log-Step "2" "检查Maven环境"
    
    # 检查Maven命令
    if (-not (Test-Command "mvn" "Apache Maven")) {
        return $false
    }
    
    # 获取Maven版本信息
    try {
        $mavenVersion = & mvn -version 2>&1 | Select-Object -First 1
        Log-Info "Maven版本：$mavenVersion"
    }
    catch {
        Log-Warning "无法获取Maven版本信息"
    }
    
    # 配置Maven使用Java 8
    $env:MAVEN_OPTS = "-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g"
    
    if ($env:JAVA_HOME) {
        $env:MAVEN_OPTS += " -Djava.home=$env:JAVA_HOME"
        Log-Info "Maven配置使用Java：$env:JAVA_HOME"
    }
    
    Log-Info "Maven选项：$env:MAVEN_OPTS"
    Log-Success "Maven环境检查完成"
    
    return $true
}

function Test-ProjectStructure {
    Log-Step "3" "验证项目结构"
    
    # 检查根目录pom.xml
    if (-not (Test-Path "pom.xml")) {
        Log-Error "根目录pom.xml文件不存在"
        return $false
    }
    Log-Info "根目录pom.xml文件存在"
    
    # 检查各个模块目录
    $missingModules = @()
    foreach ($module in $PROJECT_MODULES) {
        if (-not (Test-Path $module)) {
            $missingModules += $module
        }
        else {
            Log-Info "模块目录存在：$module"
            
            # 检查模块的pom.xml
            if (-not (Test-Path "$module\pom.xml")) {
                Log-Warning "模块pom.xml不存在：$module\pom.xml"
            }
        }
    }
    
    # 报告缺失的模块
    if ($missingModules.Count -gt 0) {
        Log-Warning "以下模块目录不存在：$($missingModules -join ', ')"
        Log-Warning "将跳过这些模块的编译"
    }
    
    Log-Success "项目结构验证完成"
    return $true
}

function Clear-Environment {
    Log-Step "4" "清理构建和测试环境"
    
    # 清理Maven构建缓存
    Log-Info "清理Maven构建缓存..."
    
    # 清理根目录target
    if (Test-Path "target") {
        Remove-Item -Path "target" -Recurse -Force
        Log-Info "清理根目录target目录"
    }
    
    # 清理各模块的target目录
    foreach ($module in $PROJECT_MODULES) {
        $targetPath = "$module\target"
        if ((Test-Path $module) -and (Test-Path $targetPath)) {
            Remove-Item -Path $targetPath -Recurse -Force
            Log-Info "清理模块target目录：$module"
        }
    }
    
    # 清理测试数据目录
    $testDataDirs = @(".\test-data", ".\data", ".\file-transfer-demo\data")
    foreach ($dir in $testDataDirs) {
        if (Test-Path $dir) {
            Remove-Item -Path $dir -Recurse -Force
            Log-Info "清理测试数据目录：$dir"
        }
    }
    
    # 清理临时文件
    Get-ChildItem -Path . -Recurse -Include "*.tmp", "test-*.dat", "*.test" -ErrorAction SilentlyContinue | Remove-Item -Force
    Get-ChildItem -Path . -Recurse -Path "*/target/*" -Include "*.log" -ErrorAction SilentlyContinue | Remove-Item -Force
    
    # 停止可能运行的测试服务器
    try {
        $testServerProcesses = Get-NetTCPConnection -LocalPort $TEST_SERVER_PORT -ErrorAction SilentlyContinue
        if ($testServerProcesses) {
            foreach ($process in $testServerProcesses) {
                $pid = $process.OwningProcess
                Log-Info "停止占用端口 $TEST_SERVER_PORT 的进程：$pid"
                Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
            }
            Start-Sleep -Seconds 2
        }
    }
    catch {
        # 忽略端口检查错误
    }
    
    Log-Success "环境清理完成"
    return $true
}

# ==================== 构建功能函数 ====================

function Invoke-ProjectCompile {
    Log-Step "5" "编译项目"

    $startTime = Get-Date

    Log-Info "开始编译整个项目..."
    Log-Info "编译命令：mvn clean compile -T 1C"

    try {
        # 执行Maven编译，使用并行编译提高速度
        $process = Start-Process -FilePath "mvn" -ArgumentList @(
            "clean", "compile", "-T", "1C",
            "-Dmaven.compiler.source=1.8",
            "-Dmaven.compiler.target=1.8", 
            "-Dmaven.compiler.encoding=UTF-8",
            "-Dproject.build.sourceEncoding=UTF-8"
        ) -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\compile-output.log" -RedirectStandardError "$LOG_DIR\compile-error.log"

        $endTime = Get-Date
        $duration = [math]::Round(($endTime - $startTime).TotalSeconds)

        if ($process.ExitCode -eq 0) {
            Log-Success "项目编译成功，耗时：${duration}秒"
            return $true
        }
        else {
            Log-Error "项目编译失败，耗时：${duration}秒"
            Log-Error "详细错误信息请查看日志文件：$MAIN_LOG"
            return $false
        }
    }
    catch {
        $endTime = Get-Date
        $duration = [math]::Round(($endTime - $startTime).TotalSeconds)
        Log-Error "项目编译过程中发生异常，耗时：${duration}秒"
        Log-Error "异常信息：$($_.Exception.Message)"
        return $false
    }
}

function Install-Project {
    Log-Step "6" "安装项目到本地Maven仓库"

    $startTime = Get-Date

    Log-Info "开始安装项目到本地Maven仓库..."
    Log-Info "安装命令：mvn install -DskipTests -T 1C"

    try {
        # 执行Maven安装，跳过测试以提高速度
        $process = Start-Process -FilePath "mvn" -ArgumentList @(
            "install", "-DskipTests", "-T", "1C",
            "-Dmaven.compiler.source=1.8",
            "-Dmaven.compiler.target=1.8",
            "-Dmaven.compiler.encoding=UTF-8",
            "-Dproject.build.sourceEncoding=UTF-8"
        ) -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\install-output.log" -RedirectStandardError "$LOG_DIR\install-error.log"

        $endTime = Get-Date
        $duration = [math]::Round(($endTime - $startTime).TotalSeconds)

        if ($process.ExitCode -eq 0) {
            Log-Success "项目安装成功，耗时：${duration}秒"
            return $true
        }
        else {
            Log-Error "项目安装失败，耗时：${duration}秒"
            Log-Error "详细错误信息请查看日志文件：$MAIN_LOG"
            return $false
        }
    }
    catch {
        $endTime = Get-Date
        $duration = [math]::Round(($endTime - $startTime).TotalSeconds)
        Log-Error "项目安装过程中发生异常，耗时：${duration}秒"
        Log-Error "异常信息：$($_.Exception.Message)"
        return $false
    }
}

function Test-BuildResults {
    Log-Step "7" "验证构建结果"

    $successCount = 0
    $totalCount = 0

    # 检查各模块的编译结果
    foreach ($module in $PROJECT_MODULES) {
        if (-not (Test-Path $module)) {
            continue
        }

        $totalCount++

        # 检查target/classes目录是否存在
        $classesPath = "$module\target\classes"
        if (Test-Path $classesPath) {
            $classFiles = Get-ChildItem -Path $classesPath -Recurse -Filter "*.class" -ErrorAction SilentlyContinue
            $classCount = $classFiles.Count
            
            if ($classCount -gt 0) {
                Log-Info "模块编译成功：$module (生成 $classCount 个class文件)"
                $successCount++
            }
            else {
                Log-Warning "模块编译异常：$module (未生成class文件)"
            }
        }
        else {
            Log-Warning "模块编译失败：$module (target\classes目录不存在)"
        }

        # 检查JAR文件是否生成
        $targetPath = "$module\target"
        if (Test-Path $targetPath) {
            $jarFiles = Get-ChildItem -Path $targetPath -Filter "*.jar" -ErrorAction SilentlyContinue
            $jarCount = $jarFiles.Count
            if ($jarCount -gt 0) {
                Log-Info "模块JAR文件生成：$module ($jarCount 个JAR文件)"
            }
        }
    }

    # 输出验证结果
    Log-Info "编译验证结果：$successCount/$totalCount 个模块编译成功"

    if ($successCount -eq $totalCount) {
        Log-Success "所有模块编译验证通过"
        return $true
    }
    else {
        Log-Warning "部分模块编译验证失败"
        return $false
    }
}

# ==================== 测试功能函数 ====================

function Invoke-UnitTests {
    Log-Step "8" "运行单元测试"

    $totalModules = 0
    $successModules = 0
    $startTime = Get-Date

    foreach ($module in $PROJECT_MODULES) {
        if (-not (Test-Path $module)) {
            Log-Warning "模块目录不存在，跳过：$module"
            continue
        }

        $totalModules++
        Log-Info "运行模块单元测试：$module"

        try {
            # 运行单元测试，排除集成测试
            $process = Start-Process -FilePath "mvn" -ArgumentList @(
                "test", "-pl", $module,
                "-Dmaven.compiler.source=1.8",
                "-Dmaven.compiler.target=1.8",
                "-Dtest=!**/*IntegrationTest,!**/*EndToEndTest"
            ) -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\test-$module-output.log" -RedirectStandardError "$LOG_DIR\test-$module-error.log"

            if ($process.ExitCode -eq 0) {
                Log-Success "模块单元测试通过：$module"
                $successModules++
            }
            else {
                Log-Error "模块单元测试失败：$module"
            }
        }
        catch {
            Log-Error "模块单元测试异常：$module - $($_.Exception.Message)"
        }
    }

    $endTime = Get-Date
    $duration = [math]::Round(($endTime - $startTime).TotalSeconds)

    Log-Info "单元测试结果：$successModules/$totalModules 个模块通过，耗时：${duration}秒"

    if ($successModules -eq $totalModules) {
        Log-Success "所有单元测试通过"
        return $true
    }
    else {
        Log-Error "部分单元测试失败"
        return $false
    }
}

function Start-TestServer {
    Log-Info "启动测试服务器（端口：$TEST_SERVER_PORT）..."

    # 确保端口未被占用
    try {
        $connection = Get-NetTCPConnection -LocalPort $TEST_SERVER_PORT -ErrorAction SilentlyContinue
        if ($connection) {
            Log-Error "端口 $TEST_SERVER_PORT 已被占用"
            return $false
        }
    }
    catch {
        # 端口未被占用，继续执行
    }

    try {
        # 启动Demo服务器作为测试服务器
        $process = Start-Process -FilePath "mvn" -ArgumentList @(
            "spring-boot:run", "-pl", "file-transfer-demo",
            "-Dspring-boot.run.arguments=--server.port=$TEST_SERVER_PORT --logging.level.com.sdesrd.filetransfer=INFO"
        ) -PassThru

        $script:TestServerProcess = $process
        $process.Id | Out-File -FilePath "$LOG_DIR\test-server.pid" -Encoding UTF8

        # 等待服务器启动
        Log-Info "等待测试服务器启动（PID：$($process.Id)）..."
        $waitCount = 0
        while ($waitCount -lt $SERVER_STARTUP_TIMEOUT) {
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:$TEST_SERVER_PORT/filetransfer/api/file/health" -TimeoutSec 1 -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    Log-Success "测试服务器启动成功"
                    return $true
                }
            }
            catch {
                # 继续等待
            }

            Start-Sleep -Seconds 1
            $waitCount++

            # 检查进程是否还在运行
            if ($process.HasExited) {
                Log-Error "测试服务器进程意外退出"
                return $false
            }
        }

        Log-Error "测试服务器启动超时"
        Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
        return $false
    }
    catch {
        Log-Error "启动测试服务器时发生异常：$($_.Exception.Message)"
        return $false
    }
}

function Stop-TestServer {
    Log-Info "停止测试服务器..."

    $pidFile = "$LOG_DIR\test-server.pid"
    if (Test-Path $pidFile) {
        try {
            $serverPid = Get-Content $pidFile -ErrorAction SilentlyContinue
            if ($serverPid) {
                $process = Get-Process -Id $serverPid -ErrorAction SilentlyContinue
                if ($process) {
                    Stop-Process -Id $serverPid -Force -ErrorAction SilentlyContinue
                    Start-Sleep -Seconds 3

                    # 检查是否还在运行
                    $process = Get-Process -Id $serverPid -ErrorAction SilentlyContinue
                    if ($process) {
                        Stop-Process -Id $serverPid -Force -ErrorAction SilentlyContinue
                    }

                    Log-Info "测试服务器已停止（PID：$serverPid）"
                }
            }
            Remove-Item $pidFile -Force -ErrorAction SilentlyContinue
        }
        catch {
            # 忽略停止服务器的错误
        }
    }

    # 确保端口释放
    try {
        $remainingProcesses = Get-NetTCPConnection -LocalPort $TEST_SERVER_PORT -ErrorAction SilentlyContinue
        if ($remainingProcesses) {
            foreach ($conn in $remainingProcesses) {
                $pid = $conn.OwningProcess
                Log-Warning "强制停止占用端口 $TEST_SERVER_PORT 的进程：$pid"
                Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
            }
        }
    }
    catch {
        # 忽略端口检查错误
    }
}

function Invoke-IntegrationTests {
    Log-Step "9" "运行集成测试"

    $startTime = Get-Date

    # 启动测试服务器
    if (-not (Start-TestServer)) {
        Log-Error "无法启动测试服务器，跳过集成测试"
        return $false
    }

    # 运行端到端测试
    Log-Info "运行端到端测试..."
    $integrationSuccess = $true

    try {
        $process = Start-Process -FilePath "mvn" -ArgumentList @(
            "test", "-pl", "file-transfer-demo",
            "-Dtest=EndToEndTransferTest",
            "-Dserver.port=$TEST_SERVER_PORT",
            "-Dmaven.compiler.source=1.8",
            "-Dmaven.compiler.target=1.8"
        ) -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\integration-output.log" -RedirectStandardError "$LOG_DIR\integration-error.log"

        if ($process.ExitCode -eq 0) {
            Log-Success "端到端测试通过"
        }
        else {
            Log-Error "端到端测试失败"
            $integrationSuccess = $false
        }
    }
    catch {
        Log-Error "端到端测试异常：$($_.Exception.Message)"
        $integrationSuccess = $false
    }

    # 停止测试服务器
    Stop-TestServer

    $endTime = Get-Date
    $duration = [math]::Round(($endTime - $startTime).TotalSeconds)

    Log-Info "集成测试耗时：${duration}秒"

    if ($integrationSuccess) {
        Log-Success "集成测试通过"
        return $true
    }
    else {
        Log-Error "集成测试失败"
        return $false
    }
}

function Invoke-PerformanceTests {
    Log-Step "10" "运行性能测试"

    $startTime = Get-Date
    Log-Info "运行性能测试..."

    try {
        # 运行服务端SDK性能测试
        $process = Start-Process -FilePath "mvn" -ArgumentList @(
            "test", "-pl", "file-transfer-server-sdk",
            "-Dtest=FileTransferPerformanceTest",
            "-Dmaven.compiler.source=1.8",
            "-Dmaven.compiler.target=1.8"
        ) -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\performance-output.log" -RedirectStandardError "$LOG_DIR\performance-error.log"

        $endTime = Get-Date
        $duration = [math]::Round(($endTime - $startTime).TotalSeconds)

        if ($process.ExitCode -eq 0) {
            Log-Success "性能测试通过"
            Log-Info "性能测试耗时：${duration}秒"
            return $true
        }
        else {
            Log-Warning "性能测试未通过或未达到预期性能指标"
            Log-Info "性能测试耗时：${duration}秒"
            return $false
        }
    }
    catch {
        $endTime = Get-Date
        $duration = [math]::Round(($endTime - $startTime).TotalSeconds)
        Log-Warning "性能测试异常：$($_.Exception.Message)"
        Log-Info "性能测试耗时：${duration}秒"
        return $false
    }
}

function New-CoverageReport {
    Log-Step "11" "生成测试覆盖率报告"

    Log-Info "生成JaCoCo测试覆盖率报告..."

    # 检查是否配置了JaCoCo插件
    try {
        $pomContent = Get-Content "pom.xml" -Raw
        if ($pomContent -like "*jacoco-maven-plugin*") {
            Log-Info "检测到JaCoCo插件配置，生成覆盖率报告..."

            # 尝试生成覆盖率报告
            $process = Start-Process -FilePath "mvn" -ArgumentList @("jacoco:report", "-q") -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\jacoco-output.log" -RedirectStandardError "$LOG_DIR\jacoco-error.log"

            if ($process.ExitCode -eq 0) {
                Log-Success "覆盖率报告生成成功"

                # 复制覆盖率报告到统一目录
                foreach ($module in $PROJECT_MODULES) {
                    $sourceDir = "$module\target\site\jacoco"
                    if (Test-Path $sourceDir) {
                        $targetDir = "$COVERAGE_REPORT_DIR\$module"
                        if (-not (Test-Path $targetDir)) {
                            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
                        }
                        Copy-Item -Path "$sourceDir\*" -Destination $targetDir -Recurse -Force -ErrorAction SilentlyContinue
                        Log-Info "覆盖率报告已复制：$targetDir"
                    }
                }

                return $true
            }
            else {
                Log-Warning "JaCoCo覆盖率报告生成失败"
                return $false
            }
        }
        else {
            Log-Info "未检测到JaCoCo插件配置，跳过覆盖率报告生成"
            Log-Info "如需生成覆盖率报告，请在父POM中配置JaCoCo插件"
            return $true
        }
    }
    catch {
        Log-Warning "检查JaCoCo配置时发生异常：$($_.Exception.Message)"
        return $false
    }
}

# ==================== 报告生成函数 ====================

function Get-TestResults {
    $totalTests = 0
    $failedTests = 0
    $skippedTests = 0

    # 收集各模块的测试结果
    foreach ($module in $PROJECT_MODULES) {
        $reportsDir = "$module\target\surefire-reports"
        if (-not (Test-Path $reportsDir)) {
            continue
        }

        # 统计测试文件数量
        $testFiles = Get-ChildItem -Path $reportsDir -Filter "TEST-*.xml" -ErrorAction SilentlyContinue

        if ($testFiles.Count -gt 0) {
            # 解析测试结果XML文件
            $moduleTests = 0
            $moduleFailures = 0
            $moduleErrors = 0
            $moduleSkipped = 0

            foreach ($xmlFile in $testFiles) {
                try {
                    [xml]$xmlContent = Get-Content $xmlFile.FullName
                    $testsuite = $xmlContent.testsuite
                    
                    if ($testsuite) {
                        $moduleTests += [int]($testsuite.tests -as [int])
                        $moduleFailures += [int]($testsuite.failures -as [int])
                        $moduleErrors += [int]($testsuite.errors -as [int])
                        $moduleSkipped += [int]($testsuite.skipped -as [int])
                    }
                }
                catch {
                    # 忽略XML解析错误
                }
            }

            $moduleFailed = $moduleFailures + $moduleErrors

            $totalTests += $moduleTests
            $failedTests += $moduleFailed
            $skippedTests += $moduleSkipped

            Log-Info "$module`: $moduleTests 个测试，$moduleFailed 个失败，$moduleSkipped 个跳过"
        }
        else {
            Log-Warning "$module`: 未找到测试结果文件"
        }
    }

    $passedTests = $totalTests - $failedTests - $skippedTests

    # 输出测试结果汇总
    Write-Host ""
    Write-Host "=========================================="
    Write-Host "           测试结果汇总"
    Write-Host "=========================================="
    Write-Host "总测试数: $totalTests"
    Write-Host "通过测试: $passedTests"
    Write-Host "失败测试: $failedTests"
    Write-Host "跳过测试: $skippedTests"

    if ($failedTests -eq 0) {
        Write-Host "测试结果: " -NoNewline
        Write-Host "全部通过" -ForegroundColor $COLOR_GREEN
        return $true
    }
    else {
        Write-Host "测试结果: " -NoNewline
        Write-Host "有失败" -ForegroundColor $COLOR_RED
        return $false
    }
}

function New-FinalReport {
    Log-Step "12" "生成最终报告"

    $reportFile = "$LOG_DIR\final-report-$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"

    $reportContent = @"
========================================================
        文件传输SDK构建和测试最终报告
========================================================
执行时间：$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
脚本版本：$SCRIPT_VERSION
Java版本：$(& java -version 2>&1 | Select-Object -First 1)
Maven版本：$(& mvn -version 2>&1 | Select-Object -First 1)

执行模式：
$(if ($script:BUILD_ONLY_MODE) { "  构建模式：仅编译项目" } else { "  完整模式：构建 + 测试" })

项目模块：
$($PROJECT_MODULES | ForEach-Object { 
    if (Test-Path $_) { "  ✓ $_" } else { "  ✗ $_ (目录不存在)" }
} | Out-String)

构建结果：
$($PROJECT_MODULES | ForEach-Object {
    if (-not (Test-Path $_)) { return }
    $classesPath = "$_\target\classes"
    if (Test-Path $classesPath) {
        $classCount = (Get-ChildItem -Path $classesPath -Recurse -Filter "*.class" -ErrorAction SilentlyContinue).Count
        "  $($_): 编译成功 ($classCount 个class文件)"
    } else {
        "  $($_): 编译失败"
    }
} | Out-String)

$(if (-not $script:BUILD_ONLY_MODE) {
@"
测试结果详情：
$($PROJECT_MODULES | ForEach-Object {
    $reportsDir = "$_\target\surefire-reports"
    if (-not (Test-Path $reportsDir)) {
        "  $($_): 未运行测试"
    } else {
        $testFiles = Get-ChildItem -Path $reportsDir -Filter "TEST-*.xml" -ErrorAction SilentlyContinue
        if ($testFiles.Count -gt 0) {
            "  $($_): $($testFiles.Count) 个测试套件"
        } else {
            "  $($_): 无测试结果"
        }
    }
} | Out-String)

覆盖率报告：
$(if (Test-Path $COVERAGE_REPORT_DIR) {
    $PROJECT_MODULES | ForEach-Object {
        if (Test-Path "$COVERAGE_REPORT_DIR\$_") {
            "  $($_): $COVERAGE_REPORT_DIR\$_\index.html"
        }
    } | Out-String
} else {
    "  未生成覆盖率报告"
})
"@
})

详细日志：$MAIN_LOG
========================================================
"@

    $reportContent | Out-File -FilePath $reportFile -Encoding UTF8

    Log-Info "最终报告已生成：$reportFile"

    # 显示报告内容
    Write-Host $reportContent

    return $true
}

# ==================== 清理和退出函数 ====================

function Invoke-Cleanup {
    # 停止测试服务器
    Stop-TestServer

    # 恢复原始环境变量
    if ($script:ORIGINAL_JAVA_HOME) {
        $env:JAVA_HOME = $script:ORIGINAL_JAVA_HOME
    }
    
    if ($script:ORIGINAL_PATH) {
        $env:PATH = $script:ORIGINAL_PATH
    }
}

# ==================== 帮助信息 ====================

function Show-Help {
    Write-Host "========================================================"
    Write-Host "    $SCRIPT_NAME"
    Write-Host "    版本：$SCRIPT_VERSION"
    Write-Host "========================================================"
    Write-Host ""
    Write-Host "用法: .\build-and-test.ps1 [选项]"
    Write-Host ""
    Write-Host "执行模式选项："
    Write-Host "  -BuildOnly                仅执行构建（编译+安装），不运行测试"
    Write-Host "  -BuildAndTest             执行完整流程（构建+测试）[默认]"
    Write-Host ""
    Write-Host "Java环境选项："
    Write-Host "  -JavaHome PATH            指定Java JDK路径"
    Write-Host "  -UseDefaultJava           使用系统默认Java（忽略脚本中的默认Java 8路径）"
    Write-Host ""
    Write-Host "测试控制选项（仅在-BuildAndTest模式下有效）："
    Write-Host "  -UnitOnly                 仅运行单元测试"
    Write-Host "  -IntegrationOnly          仅运行集成测试"
    Write-Host "  -Performance              包含性能测试"
    Write-Host "  -SkipCoverage             跳过覆盖率报告生成"
    Write-Host ""
    Write-Host "执行控制选项："
    Write-Host "  -NoCleanup                执行前不清理环境"
    Write-Host "  -NoReport                 不生成最终报告"
    Write-Host "  -Verbose                  显示详细输出"
    Write-Host "  -Help                     显示此帮助信息"
    Write-Host ""
    Write-Host "环境变量："
    Write-Host "  JAVA_HOME                 可通过环境变量指定Java路径"
    Write-Host "  MAVEN_OPTS                Maven执行选项（脚本会自动设置）"
    Write-Host ""
    Write-Host "使用示例："
    Write-Host "  .\build-and-test.ps1                                 # 完整构建和测试流程"
    Write-Host "  .\build-and-test.ps1 -BuildOnly                      # 仅构建项目"
    Write-Host "  .\build-and-test.ps1 -JavaHome 'C:\Java\jdk1.8.0'   # 使用指定的Java路径"
    Write-Host "  .\build-and-test.ps1 -UnitOnly                       # 构建后仅运行单元测试"
    Write-Host "  .\build-and-test.ps1 -Performance                    # 包含性能测试的完整流程"
    Write-Host "  .\build-and-test.ps1 -Verbose                        # 显示详细执行过程"
    Write-Host ""
    Write-Host "默认配置："
    Write-Host "  Java路径: $DEFAULT_JAVA8_HOME"
    Write-Host "  测试端口: $TEST_SERVER_PORT"
    Write-Host "  日志目录: $LOG_DIR"
    Write-Host ""
}

# ==================== 主程序 ====================

function Main {
    # 显示帮助信息
    if ($Help) {
        Show-Help
        return
    }

    # 设置执行模式
    $script:BUILD_ONLY_MODE = $BuildOnly

    # 显示脚本头部信息
    Show-Header

    # 初始化日志
    Initialize-Logging

    # 如果启用详细模式，设置调试首选项
    if ($Verbose) {
        $VerbosePreference = "Continue"
        Log-Info "启用详细输出模式"
    }

    # 执行主要流程
    $executionFailed = $false

    try {
        # 步骤1-4：环境检查和准备
        if ($UseDefaultJava) {
            if (-not (Setup-JavaEnvironment "")) {
                $executionFailed = $true
            }
        }
        else {
            if (-not (Setup-JavaEnvironment $JavaHome)) {
                $executionFailed = $true
            }
        }

        if (-not $executionFailed -and -not (Test-MavenEnvironment)) {
            $executionFailed = $true
        }
        elseif (-not $executionFailed -and -not (Test-ProjectStructure)) {
            $executionFailed = $true
        }
        elseif (-not $executionFailed -and -not $NoCleanup -and -not (Clear-Environment)) {
            $executionFailed = $true
        }
        # 步骤5-7：构建流程
        elseif (-not $executionFailed -and -not (Invoke-ProjectCompile)) {
            $executionFailed = $true
        }
        elseif (-not $executionFailed -and -not (Install-Project)) {
            $executionFailed = $true
        }
        elseif (-not $executionFailed -and -not (Test-BuildResults)) {
            $executionFailed = $true
        }

        # 步骤8-11：测试流程（仅在非build-only模式下执行）
        if (-not $executionFailed -and -not $script:BUILD_ONLY_MODE) {
            if (-not $IntegrationOnly -and -not (Invoke-UnitTests)) {
                $executionFailed = $true
            }
            elseif (-not $UnitOnly -and -not (Invoke-IntegrationTests)) {
                $executionFailed = $true
            }
            elseif ($Performance -and -not (Invoke-PerformanceTests)) {
                Log-Warning "性能测试未通过，但不影响整体执行结果"
            }

            # 生成覆盖率报告
            if (-not $SkipCoverage) {
                New-CoverageReport | Out-Null
            }

            # 收集测试结果
            if (-not (Get-TestResults)) {
                $executionFailed = $true
            }
        }

        # 步骤12：生成最终报告
        if (-not $NoReport) {
            New-FinalReport | Out-Null
        }

        # 返回结果
        if ($executionFailed) {
            Log-Error "执行失败"
            exit 1
        }
        else {
            if ($script:BUILD_ONLY_MODE) {
                Log-Success "构建成功完成"
            }
            else {
                Log-Success "构建和测试成功完成"
            }
            exit 0
        }
    }
    catch {
        Log-Error "执行过程中发生异常：$($_.Exception.Message)"
        Log-Error "详细错误信息请查看日志文件：$MAIN_LOG"
        exit 1
    }
    finally {
        # 清理资源
        Invoke-Cleanup
    }
}

# 设置错误处理
trap {
    Log-Error "脚本执行过程中发生未处理的异常：$($_.Exception.Message)"
    Log-Error "详细错误信息请查看日志文件：$MAIN_LOG"
    Invoke-Cleanup
    exit 1
}

# 执行主函数
Main 